//
//  PLVFdUtil.m
//  PLVFoundationSDK
//
//  Created by ftao on 2019/8/28.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVFdUtil.h"
#import "PLVProgressHUD.h"
#import "PLVFDI18NUtil.h"
#import <CoreText/CoreText.h>
#import <sys/utsname.h>
#import "PLVFConsoleLogger.h"

#pragma mark - Functions

NSString* PLV_SafeStringForValue(id value) {
    if ([value isKindOfClass:NSString.class]) {
        return value;
    } else if ([value isKindOfClass:NSNumber.class]) {
        return [(NSNumber *)value stringValue];
    } else {
        return nil;
    }
}

NSArray* PLV_SafeArraryForValue(id value) {
    if ([value isKindOfClass:NSArray.class]) {
        return value;
    } else {
        return nil;
    }
}

NSDictionary* PLV_SafeDictionaryForValue(id value) {
    if ([value isKindOfClass:NSDictionary.class]) {
        return value;
    } else {
        return nil;
    }
}

BOOL PLV_SafeBoolForValue(id value) {
    if ([value isKindOfClass:NSString.class]) {
        return [(NSString *)value boolValue];
    } else if ([value isKindOfClass:NSNumber.class]) {
        return [(NSNumber *)value boolValue];
    } else {
        return NO;
    }
}

float PLV_SafeFloatForValue(id value) {
    if ([value isKindOfClass:NSString.class]) {
        return [(NSString *)value floatValue];
    } else if ([value isKindOfClass:NSNumber.class]) {
        return [(NSNumber *)value floatValue];
    } else {
        return 0.0;
    }
}

NSInteger PLV_SafeIntegerForValue(id value) {
    if ([value isKindOfClass:NSString.class]) {
        return [(NSString *)value integerValue];
    } else if ([value isKindOfClass:NSNumber.class]) {
        return [(NSNumber *)value integerValue];
    } else {
        return 0;
    }
}

#pragma mark dict value for key

NSString* PLV_SafeStringForDictKey(NSDictionary *dict, NSString *aKey) {
    if ([dict isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeStringForValue(dict[aKey]);
    }
    return nil;
}

NSArray* PLV_SafeArraryForDictKey(NSDictionary *dict, NSString *aKey) {
    if ([dict isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeArraryForValue(dict[aKey]);
    }
    return nil;
}

NSDictionary* PLV_SafeDictionaryForDictKey(NSDictionary *dict, NSString *aKey) {
    if ([dict isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeDictionaryForValue(dict[aKey]);
    }
    return nil;
}

BOOL PLV_SafeBoolForDictKey(NSDictionary *dict, NSString *aKey) {
    if ([dict isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeBoolForValue(dict[aKey]);
    }
    return NO;
}

float PLV_SafeFloatForDictKey(NSDictionary *dict, NSString *aKey) {
    if ([dict isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeFloatForValue(dict[aKey]);
    }
    return 0.0;
}

NSInteger PLV_SafeIntegerForDictKey(NSDictionary *json, NSString *aKey) {
    if ([json isKindOfClass:NSDictionary.class] && aKey) {
        return PLV_SafeIntegerForValue(json[aKey]);
    }
    return 0;
}

CGFloat P_SafeAreaTopEdgeInsets(void) {
    UIEdgeInsets edgeInsets = UIEdgeInsetsZero;
    if (@available(iOS 11.0, *)) {
        UIWindow *applicationWindow = [PLVFdUtil getFirstUIWindowFormUIApplication];
        edgeInsets = applicationWindow.safeAreaInsets;
    }
    return edgeInsets.top;
}

CGFloat P_SafeAreaLeftEdgeInsets(void) {
    UIEdgeInsets edgeInsets = UIEdgeInsetsZero;
    if (@available(iOS 11.0, *)) {
        UIWindow *applicationWindow = [PLVFdUtil getFirstUIWindowFormUIApplication];
        edgeInsets = applicationWindow.safeAreaInsets;
    }
    return edgeInsets.left;
}

CGFloat P_SafeAreaBottomEdgeInsets(void) {
    UIEdgeInsets edgeInsets = UIEdgeInsetsZero;
    if (@available(iOS 11.0, *)) {
        UIWindow *applicationWindow = [PLVFdUtil getFirstUIWindowFormUIApplication];
        edgeInsets = applicationWindow.safeAreaInsets;
    }
    return edgeInsets.bottom;
}

CGFloat P_SafeAreaRightEdgeInsets(void) {
    UIEdgeInsets edgeInsets = UIEdgeInsetsZero;
    if (@available(iOS 11.0, *)) {
        UIWindow *applicationWindow = [PLVFdUtil getFirstUIWindowFormUIApplication];
        edgeInsets = applicationWindow.safeAreaInsets;
    }
    return edgeInsets.right;
}

NSError * PLVErrorCreate(NSString *domain, NSInteger code, NSString *errorDescription) {
    if (code == 0) { return nil; }
    NSDictionary * userInfo;
    if ([PLVFdUtil checkStringUseable:errorDescription]) {
        userInfo = [NSDictionary dictionaryWithObject:errorDescription forKey:NSLocalizedDescriptionKey];
    }
    NSError * error = [NSError errorWithDomain:domain code:code userInfo:userInfo];
    return error;
}

NSError * PLVErrorWithUnderlyingError(NSError *error, NSError *underlyingError) {
    if (!error) { return underlyingError; }
    
    if (!underlyingError || error.userInfo[NSUnderlyingErrorKey]) { return error; }
    
    NSMutableDictionary * mutableUserInfo = [error.userInfo mutableCopy];
    mutableUserInfo[NSUnderlyingErrorKey] = underlyingError;
    return [[NSError alloc] initWithDomain:error.domain code:error.code userInfo:mutableUserInfo];
}

NSInteger PLVErrorLastErrorCode(NSError *error) {
    if (!error) { return 0; }
    NSInteger lastErrorCode = error.code;
    NSError * underlyingError = error.userInfo[NSUnderlyingErrorKey];
    int count = 0;
    while ((underlyingError && [underlyingError isKindOfClass:NSError.class] && count < 5)) {
        lastErrorCode = underlyingError.code;
        underlyingError = underlyingError.userInfo[NSUnderlyingErrorKey];
        count ++;
    }
    return lastErrorCode;
}

@implementation PLVFdUtil

#pragma mark - Dic&ArrayUtil

+ (BOOL)checkDictionaryUseable:(NSDictionary *)dict{
    if (dict && [dict isKindOfClass:NSDictionary.class] && dict.count > 0) {
        return YES;
    }
    return NO;
}

+ (BOOL)checkArrayUseable:(NSArray *)array{
    if (array && [array isKindOfClass:NSArray.class] && array.count > 0) {
        return YES;
    }
    return NO;
}

#pragma mark - StringUtil

+ (NSString *)URLEncodedString:(NSString *)url {
    if ([url isKindOfClass:[NSNull class]] || !url.length) {
        return @"";
    }
    
    NSString *newStr = [url stringByReplacingOccurrencesOfString:@" " withString:@"+"];
    //    NSString *encodeStr = (NSString *)CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(kCFAllocatorDefault, (CFStringRef)newStr, NULL, CFSTR("!*'();:@&=+$,/?%#[]\" "), kCFStringEncodingUTF8));// 字符串中含有!*'();:@&=+$,/?%#[]\" 这些字符时候会被转化
    
    //CFURLCreateStringByAddingPercentEscapes有警告，用【NSString stringByAddingPercentEncodingWithAllowedCharacters:]代替
    NSString *charactersToEscape = @"!*'();:@&=+$,/?%#[]\" <>|{}^`";
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
    NSString *encodeStr = [newStr stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
    
    return encodeStr;
}

+ (NSString *)packageURLStringWithHTTPS:(NSString *)urlString {
    while ([urlString hasPrefix:@"/"] || [urlString hasPrefix:@":"]) {
        urlString = [urlString substringFromIndex:1];
    }
    
    NSString *resultString = urlString;
    if ([urlString rangeOfString:@"http"].location == NSNotFound) {
        resultString = [NSString stringWithFormat:@"https://%@", urlString];
    } else if ([urlString rangeOfString:@"https"].location == NSNotFound) {
        resultString = [NSString stringWithFormat:@"https://%@", [urlString substringFromIndex:7]];
    }
    return resultString;
}

/// 检查字符串是否可用(YES-可用；NO-不可用)
+ (BOOL)checkStringUseable:(NSString *)string{
    if (string && [string isKindOfClass:NSString.class] && string.length > 0) {
        return YES;
    }
    return NO;
}

+ (NSString *)stringBySafeAddingPercentEncoding:(NSString *)urlStr {
    if ([urlStr isKindOfClass:NSString.class]) {
        if ([urlStr isEqualToString:urlStr.stringByRemovingPercentEncoding]) {
            return [urlStr stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        } else {
            return urlStr;
        }
    } else {
        return nil;
    }
}

+ (NSString *)cutSting:(NSString *)string WithCharacterLength:(NSInteger)length {
    float sum = 0;
    NSInteger index = 0;
    for (int i = 0; i < string.length; i++) {
        NSString *character = [string substringWithRange:NSMakeRange(i, 1)];
        if ([character lengthOfBytesUsingEncoding:NSUTF8StringEncoding] == 3) {
            sum += 1;
        } else {
            sum += 0.5;
        }
        if (sum >= length) {
            index = i;
            break;
        }
    }
    if (index == 0) {
        return string;
    } else {
        NSString *subString = [string substringWithRange:NSMakeRange(0, index+1)];
        return [NSString stringWithFormat:@"%@...", subString];
    }
}

#pragma mark - UILabel Util

+ (NSDictionary *)textAttributesAtPoint:(CGPoint)point withLabel:(UILabel *)label {
    NSDictionary *dict = nil;
    
    // First, create a CoreText framesetter
    CTFramesetterRef framesetter = CTFramesetterCreateWithAttributedString((__bridge CFAttributedStringRef)label.attributedText);
    CGMutablePathRef framePath = CGPathCreateMutable();
    CGPathAddRect(framePath, NULL, CGRectMake(0, 0, label.frame.size.width, label.frame.size.height));
    // Get the frames that will do the rendering
    CFRange currentRange = CFRangeMake(0, 0);
    CTFrameRef frameRef = CTFramesetterCreateFrame(framesetter, currentRange, framePath, NULL);
    CGPathRelease(framePath);
    // Get each of the typeset lines
    NSArray *lines = (__bridge id)CTFrameGetLines(frameRef);
    CFIndex linesCount = [lines count];
    CGPoint *lineOrigins = (CGPoint *)malloc(sizeof(CGPoint) * linesCount);
    CTFrameGetLineOrigins(frameRef, CFRangeMake(0, 0), lineOrigins);
    CTLineRef line = NULL;
    CGPoint lineOrigin = CGPointZero;
    // Correct each of the typeset lines (which have origin (0,0)) to the correct orientation (typesetting offsets from the bottom to the frame)
    CGFloat bottom = label.frame.size.height;
    for (CFIndex i = 0; i < linesCount; ++i) {
        lineOrigins[i].y = label.frame.size.height - lineOrigins[i].y;
        bottom = lineOrigins[i].y;
    }
    // offset the touch point by the amount of space between the top of the label frame and the text
    point.y -= (label.frame.size.height - bottom) / 2.0;
    // Scan through each line to find the line containing the touch point y position
    for (CFIndex i = 0; i < linesCount; ++i) {
        line = (__bridge CTLineRef)[lines objectAtIndex:i];
        lineOrigin = lineOrigins[i];
        CGFloat descent, ascent;
        CGFloat width = CTLineGetTypographicBounds(line, &ascent, &descent, nil);
        if (point.y < (floor(lineOrigin.y) + floor(descent))) {
            // Cater for text alignment set in the label itself (not in the attributed string)
            if (label.textAlignment == NSTextAlignmentCenter) {
                point.x -= (label.frame.size.width - width) / 2.0;
            } else if (label.textAlignment == NSTextAlignmentRight) {
                point.x -= (label.frame.size.width - width);
            }
            // Offset the touch position by the actual typeset line origin. point is now the correct touch position with the line bounds
            point.x -= lineOrigin.x;
            point.y -= lineOrigin.y;
            // Find the text index within this line for the touch position
            CFIndex i = CTLineGetStringIndexForPosition(line, point);
            // Iterate through each of the glyph runs to find the run containing the character index
            NSArray *glyphRuns = (__bridge id)CTLineGetGlyphRuns(line);
            CFIndex runCount = [glyphRuns count];
            for (CFIndex run = 0; run < runCount; ++run) {
                CTRunRef glyphRun = (__bridge  CTRunRef)[glyphRuns objectAtIndex:run];
                CFRange range = CTRunGetStringRange(glyphRun);
                if ( i >= range.location && i <= range.location + range.length) {
                    dict = (__bridge NSDictionary *)CTRunGetAttributes(glyphRun);
                    break;
                }
            }
            if (dict) {
                break;
            }
        }
    }
    free(lineOrigins);
    CFRelease(frameRef);
    CFRelease(framesetter);
    return dict;
}

#pragma mark - ImageUtil

+ (void)setImageWithURL:(NSURL *)url inImageView:(UIImageView *)imageView completed:(void(^)(UIImage *image, NSError *error, NSURL *imageURL))completedBlock {
    if (!url || ![url isKindOfClass:NSURL.class]) {
        if (completedBlock) {
            NSError *err = [NSError errorWithDomain:@"net.polyv.FoundationSDK" code:-1000 userInfo:@{NSLocalizedDescriptionKey:@"Invalid url parameter."}];
            completedBlock(nil, err, nil);
        }
        return;
    }
    
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_LOW, 0), ^{
        NSError *error;
        NSData *data = [NSData dataWithContentsOfURL:url options:NSDataReadingMappedIfSafe error:&error];
        UIImage *image = [UIImage imageWithData:data];
        
        BOOL success = (error == nil && image);
        if (success) {
            dispatch_async(dispatch_get_main_queue(), ^{
                imageView.image = image;
            });
        }
        
        if (completedBlock) {
            completedBlock(image, error, url);
        }
    });
}

#pragma mark - DateUtil

+ (long long)curTimeInterval {
    return (long long)([[NSDate date] timeIntervalSince1970] * 1000);
}

+ (NSString *)curTimeStamp {
    return [NSString stringWithFormat:@"%lld", [PLVFdUtil curTimeInterval]];
}

+ (NSString *)secondsToString:(NSTimeInterval)seconds {
    NSInteger time = seconds;
    NSInteger hour = time / 3600;
    NSInteger min = (time / 60) % 60;
    NSInteger sec = time % 60;
    NSString *str = hour > 0 ? [NSString stringWithFormat:@"%02zd:", hour] : @"";
    str = [str stringByAppendingString:[NSString stringWithFormat:@"%02zd:%02zd", min, sec]];
    return str;
}

+ (NSString *)secondsToString2:(NSTimeInterval)seconds {
    NSInteger time = seconds;
    NSInteger hour = time / 3600;
    NSInteger min = (time / 60) % 60;
    NSInteger sec = time % 60;
    return [NSString stringWithFormat:@"%02zd:%02zd:%02zd",hour, min, sec];
}

+ (NSTimeInterval)secondsToTimeInterval:(NSString *)timeString {
    NSArray *timeComponents = [timeString componentsSeparatedByString:@":"];
    NSTimeInterval seconds = 0;
    int componentCount = 3;
    if (timeComponents.count < componentCount) {
        componentCount = (int)timeComponents.count;
    }
    for (int i = 0; i < componentCount; i++) {
        NSInteger timeComponent = [timeComponents[i] integerValue];
        seconds += pow(60, componentCount-1-i) * timeComponent;
    }
    return seconds;
}

#pragma mark - HudUtil

+ (void)showHUDWithTitle:(NSString *)title detail:(NSString *)detail view:(UIView *)view {
    PLVF_NORMAL_LOG_INFO(@"UI", @"HUD info title:%@, detail:%@", title, detail);
    if (view != nil) {
        PLVProgressHUD *hud = [PLVProgressHUD showHUDAddedTo:view animated:YES];
        hud.mode = PLVProgressHUDModeText;
        hud.label.superview.backgroundColor = [UIColor blackColor];
        hud.label.font = [UIFont systemFontOfSize:18.0];
        hud.label.textColor = [UIColor whiteColor];
        hud.label.text = title;
        hud.detailsLabel.font = [UIFont systemFontOfSize:15.0];
        hud.detailsLabel.textColor = [UIColor whiteColor];
        hud.detailsLabel.text = detail;
        [hud hideAnimated:YES afterDelay:2.0];
    }
}

#pragma mark - Common

+ (UIViewController *)getCurrentViewController{
    UIViewController* currentViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
    BOOL runLoopFind = YES;
    while (runLoopFind) {
        if (currentViewController.presentedViewController) {
            currentViewController = currentViewController.presentedViewController;
        } else if ([currentViewController isKindOfClass:[UINavigationController class]]) {
            UINavigationController* navigationController = (UINavigationController* )currentViewController;
            currentViewController = [navigationController.childViewControllers lastObject];
        } else if ([currentViewController isKindOfClass:[UITabBarController class]]) {
            UITabBarController* tabBarController = (UITabBarController* )currentViewController;
            currentViewController = tabBarController.selectedViewController;
        } else {
            NSUInteger childViewControllerCount = currentViewController.childViewControllers.count;
            if (childViewControllerCount > 0) {
                currentViewController = currentViewController.childViewControllers.lastObject;
                return currentViewController;
            } else {
                return currentViewController;
            }
        }
    }
    return currentViewController;
}

/// 获取UIApplication里面第一个UIWindow对象
+ (UIWindow *_Nonnull)getFirstUIWindowFormUIApplication {
    if (@available(iOS 13.0, *)) { // iOS 13.0+
        NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
        UIWindowScene *windowScene = (UIWindowScene *)array.firstObject;
        UIWindow *applicationWindow = [windowScene valueForKeyPath:@"delegate.window"];
        UIWindow *applicationTempWindow = [windowScene valueForKeyPath:@"delegate.window"];
        if (!applicationWindow) {
            NSArray *windowsArray = [UIApplication sharedApplication].windows;
            for (int i = 0; i < windowsArray.count; i++) {
                UIWindow *window = windowsArray[i];
                if (!applicationTempWindow && window.windowLevel >= UIWindowLevelNormal) {
                    applicationTempWindow = window;
                }
                if ([NSStringFromClass(window.class) isEqualToString:@"UIWindow"]) {
                    applicationWindow = window;
                    break;
                }
            }
        }
        if (!applicationWindow) {
            return !applicationTempWindow ? [UIApplication sharedApplication].windows.firstObject : applicationTempWindow;
        }
        return applicationWindow;
    } else if (@available(iOS 11.0, *)) { // iOS 11.0 ～ 12.x
        UIWindow *applicationWindow = [[[UIApplication sharedApplication] delegate] window];
        return applicationWindow;
    }
    return [UIApplication sharedApplication].windows.firstObject;
}

#pragma mark - Device

+ (BOOL)isiPhoneXSeries {
    UIEdgeInsets edgeInsets = UIEdgeInsetsZero;
    if (@available(iOS 11.0, *)) {
        UIWindow *applicationWindow = [PLVFdUtil getFirstUIWindowFormUIApplication];
        edgeInsets = applicationWindow.safeAreaInsets;
    }
    return !UIEdgeInsetsEqualToEdgeInsets(edgeInsets, UIEdgeInsetsZero);
}

+ (void)changeDeviceOrientationToPortrait {
    [self changeDeviceOrientation:UIDeviceOrientationPortrait];
}

+ (void)changeDeviceOrientation:(UIDeviceOrientation)orientation {
    if (@available(iOS 16.0, *)) {
        UIViewController *currentVC = [PLVFdUtil getCurrentViewController];
        if (currentVC && [currentVC respondsToSelector:@selector(setNeedsUpdateOfSupportedInterfaceOrientations)]) {
            [currentVC performSelector:@selector(setNeedsUpdateOfSupportedInterfaceOrientations)];
        }
        
        UIInterfaceOrientationMask mask = UIInterfaceOrientationMaskAllButUpsideDown;
        if (orientation == UIInterfaceOrientationLandscapeLeft || orientation == UIInterfaceOrientationLandscapeRight) {
            mask = UIInterfaceOrientationMaskLandscape;
        } else {
            mask = UIInterfaceOrientationMaskPortrait;
        }
        NSArray *array = [[[UIApplication sharedApplication] connectedScenes] allObjects];
        UIWindowScene *windowScene = (UIWindowScene *)array.firstObject;
        Class UIWindowSceneGeometryPreferencesIOSClass = NSClassFromString(@"UIWindowSceneGeometryPreferencesIOS");
        id geometryPreferences = [[UIWindowSceneGeometryPreferencesIOSClass alloc] init];
        if (geometryPreferences) {
            [geometryPreferences setValue:@(mask) forKey:@"interfaceOrientations"];
            SEL sel_method = NSSelectorFromString(@"requestGeometryUpdateWithPreferences:errorHandler:");
            void (^errorHandler)(NSError *error) = ^(NSError *error) {
                PLVF_NORMAL_LOG_ERROR(@"UI",@"iOS16旋转屏幕错误_%@", error);
            };
            if ([windowScene respondsToSelector:sel_method]) {
                (((void (*)(id, SEL,id,id))[windowScene methodForSelector:sel_method])(windowScene, sel_method, geometryPreferences, errorHandler));
            }
        }
    } else {
        if ([[UIDevice currentDevice] respondsToSelector:@selector(setOrientation:)]) {
            SEL selector = NSSelectorFromString(@"setOrientation:");
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:[UIDevice instanceMethodSignatureForSelector:selector]];
            [invocation setSelector:selector];
            [invocation setTarget:[UIDevice currentDevice]];
            int val = orientation;
            [invocation setArgument:&val atIndex:2];//从2开始，因为0 1 两个参数已经被selector和target占用
            [invocation invoke];
        }
    }
}

+ (NSString *)getiPhoneTypeName{
    // 产品需求上，需要设备型号。但同时又需转换成通俗的设备型号名，因此需要以下转换逻辑
    // 请及时更新以下表格
    
    struct utsname systemInfo;
    uname(&systemInfo);
    
    NSString * platform = [NSString stringWithCString:systemInfo.machine encoding:NSASCIIStringEncoding];
    
    // 模拟器
    if ([platform isEqualToString:@"i386"]) return @"iPhone Simulator";
    if ([platform isEqualToString:@"x86_64"]) return @"iPhone Simulator";
    
    
    /**iPhone*/
    // 2021年9月15日 iPhone 13 mini、13、13 Pro、13 Pro Max 发布
    if ([platform isEqualToString:@"iPhone14,4"]) return @"iPhone 13 mini";
    if ([platform isEqualToString:@"iPhone14,5"]) return @"iPhone 13";
    if ([platform isEqualToString:@"iPhone14,2"]) return @"iPhone 13 Pro";
    if ([platform isEqualToString:@"iPhone14,3"]) return @"iPhone 13 Pro Max";
    
    // 2020年10月14日，iPhone 12 mini、12、12 Pro、12 Pro Max 发布
    if ([platform isEqualToString:@"iPhone13,1"]) return @"iPhone 12 mini";
    if ([platform isEqualToString:@"iPhone13,2"]) return @"iPhone 12";
    if ([platform isEqualToString:@"iPhone13,3"]) return @"iPhone 12 Pro";
    if ([platform isEqualToString:@"iPhone13,4"]) return @"iPhone 12 Pro Max";
    
    // 2020年4月15日，iPhone SE 发布
    if ([platform isEqualToString:@"iPhone12,8"]) return @"iPhone SE 2020";
    
    // 2019年9月11日，iPhone 11，iPhone 11 Pro，iPhone 11 Pro Max 发布
    if ([platform isEqualToString:@"iPhone12,1"]) return @"iPhone 11";
    if ([platform isEqualToString:@"iPhone12,3"]) return @"iPhone 11 Pro";
    if ([platform isEqualToString:@"iPhone12,5"]) return @"iPhone 11 Pro Max";
    
    // 2018年9月13日，iPhone XS，iPhone XS Max，iPhone XR 发布
    if ([platform isEqualToString:@"iPhone11,8"]) return @"iPhone XR";
    if ([platform isEqualToString:@"iPhone11,2"]) return @"iPhone XS";
    if ([platform isEqualToString:@"iPhone11,4"]) return @"iPhone XS Max";
    if ([platform isEqualToString:@"iPhone11,6"]) return @"iPhone XS Max";
    
    // 2017年9月13日，iPhone 8，iPhone 8 Plus，iPhone X 发布
    if ([platform isEqualToString:@"iPhone10,1"]) return @"iPhone 8";
    if ([platform isEqualToString:@"iPhone10,4"]) return @"iPhone 8";
    if ([platform isEqualToString:@"iPhone10,2"]) return @"iPhone 8 Plus";
    if ([platform isEqualToString:@"iPhone10,5"]) return @"iPhone 8 Plus";
    if ([platform isEqualToString:@"iPhone10,3"]) return @"iPhone X";
    if ([platform isEqualToString:@"iPhone10,6"]) return @"iPhone X";
    
    // 2016年9月8日，iPhone 7，iPhone 7 Plus 发布
    if ([platform isEqualToString:@"iPhone9,1"]) return @"iPhone 7";
    if ([platform isEqualToString:@"iPhone9,2"]) return @"iPhone 7 Plus";
    
    // 2016年3月21日，iPhone SE 发布
    if ([platform isEqualToString:@"iPhone8,4"]) return @"iPhone SE";
    
    // 2015年9月10日，iPhone 6S，iPhone 6S Plus 发布
    if ([platform isEqualToString:@"iPhone8,1"]) return @"iPhone 6s";
    if ([platform isEqualToString:@"iPhone8,2"]) return @"iPhone 6s Plus";
    
    // 2014年9月10日，iPhone 6，iPhone 6 Plus 发布
    if ([platform isEqualToString:@"iPhone7,1"]) return @"iPhone 6 Plus";
    if ([platform isEqualToString:@"iPhone7,2"]) return @"iPhone 6";
    
    // 2013年9月10日，iPhone 5C，iPhone 5S 发布
    if ([platform isEqualToString:@"iPhone5,3"]) return @"iPhone 5c";
    if ([platform isEqualToString:@"iPhone5,4"]) return @"iPhone 5c";
    if ([platform isEqualToString:@"iPhone6,1"]) return @"iPhone 5s";
    if ([platform isEqualToString:@"iPhone6,2"]) return @"iPhone 5s";
    
    // 2012年9月13日，iPhone 5 发布
    if ([platform isEqualToString:@"iPhone5,1"]) return @"iPhone 5";
    if ([platform isEqualToString:@"iPhone5,2"]) return @"iPhone 5";
    
    // 2011年10月4日，iPhone 4S 发布
    if ([platform isEqualToString:@"iPhone4,1"]) return @"iPhone 4S";
    
    // 2010年6月8日，iPhone 4 发布
    if ([platform isEqualToString:@"iPhone3,1"]) return @"iPhone 4";
    if ([platform isEqualToString:@"iPhone3,2"]) return @"iPhone 4";
    if ([platform isEqualToString:@"iPhone3,3"]) return @"iPhone 4";
    
    // 2009年6月9日，iPhone 3GS 发布
    if ([platform isEqualToString:@"iPhone2,1"]) return @"iPhone 3GS";

    // 2008年6月10日，iPhone 3G 发布
    if ([platform isEqualToString:@"iPhone1,2"]) return @"iPhone 3G";
    
    // 2007年1月9日，iPhone 2G 发布
    if ([platform isEqualToString:@"iPhone1,1"]) return @"iPhone 2G";
    
    
    /**iPad*/
    if ([platform isEqualToString:@"iPad1,1"]) return @"iPad";
    
    if ([platform isEqualToString:@"iPad2,1"]) return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,2"]) return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,3"]) return @"iPad 2";
    if ([platform isEqualToString:@"iPad2,4"]) return @"iPad 2";
    
    if ([platform isEqualToString:@"iPad3,1"]) return @"iPad 3";
    if ([platform isEqualToString:@"iPad3,2"]) return @"iPad 3";
    if ([platform isEqualToString:@"iPad3,3"]) return @"iPad 3";
    
    if ([platform isEqualToString:@"iPad3,4"]) return @"iPad 4";
    if ([platform isEqualToString:@"iPad3,5"]) return @"iPad 4";
    if ([platform isEqualToString:@"iPad3,6"]) return @"iPad 4";
    
    if ([platform isEqualToString:@"iPad6,11"]) return @"iPad 5";
    if ([platform isEqualToString:@"iPad6,12"]) return @"iPad 5";

    if ([platform isEqualToString:@"iPad7,5"]) return @"iPad 6";
    if ([platform isEqualToString:@"iPad7,6"]) return @"iPad 6";
    
    if ([platform isEqualToString:@"iPad7,11"]) return @"iPad 7";
    if ([platform isEqualToString:@"iPad7,12"]) return @"iPad 7";
    
    if ([platform isEqualToString:@"iPad11,6"]) return @"iPad 8";
    if ([platform isEqualToString:@"iPad11,7"]) return @"iPad 8";
    
    if ([platform isEqualToString:@"iPad12,1"]) return @"iPad 9";
    if ([platform isEqualToString:@"iPad12,2"]) return @"iPad 9";

    if ([platform isEqualToString:@"iPad4,1"]) return @"iPad Air";
    if ([platform isEqualToString:@"iPad4,2"]) return @"iPad Air";
    if ([platform isEqualToString:@"iPad4,3"]) return @"iPad Air";

    if ([platform isEqualToString:@"iPad5,3"]) return @"iPad Air2";
    if ([platform isEqualToString:@"iPad5,4"]) return @"iPad Air2";
    
    if ([platform isEqualToString:@"iPad11,3"]) return @"iPad Air3";
    if ([platform isEqualToString:@"iPad11,4"]) return @"iPad Air3";

    if ([platform isEqualToString:@"iPad13,1"]) return @"iPad Air4";
    if ([platform isEqualToString:@"iPad13,2"]) return @"iPad Air4";
    
    if ([platform isEqualToString:@"iPad6,7"]) return @"iPad Pro 12.9-inch";
    if ([platform isEqualToString:@"iPad6,8"]) return @"iPad Pro 12.9-inch";
    
    if ([platform isEqualToString:@"iPad6,3"]) return @"iPad Pro 9.7-inch";
    if ([platform isEqualToString:@"iPad6,4"]) return @"iPad Pro 9.7-inch";
    
    if ([platform isEqualToString:@"iPad7,1"]) return @"iPad Pro 12.9-inch 2";
    if ([platform isEqualToString:@"iPad7,2"]) return @"iPad Pro 12.9-inch 2";
    
    if ([platform isEqualToString:@"iPad8,1"]) return @"iPad Pro 11-inch";
    if ([platform isEqualToString:@"iPad8,2"]) return @"iPad Pro 11-inch";
    if ([platform isEqualToString:@"iPad8,3"]) return @"iPad Pro 11-inch";
    if ([platform isEqualToString:@"iPad8,4"]) return @"iPad Pro 11-inch";

    if ([platform isEqualToString:@"iPad8,5"]) return @"iPad Pro 12.9-inch 3";
    if ([platform isEqualToString:@"iPad8,6"]) return @"iPad Pro 12.9-inch 3";
    if ([platform isEqualToString:@"iPad8,7"]) return @"iPad Pro 12.9-inch 3";
    if ([platform isEqualToString:@"iPad8,8"]) return @"iPad Pro 12.9-inch 3";
    
    if ([platform isEqualToString:@"iPad8,9"]) return @"iPad Pro 11-inch 2";
    if ([platform isEqualToString:@"iPad8,10"]) return @"iPad Pro 11-inch 2";
    
    if ([platform isEqualToString:@"iPad8,11"]) return @"iPad Pro 12.9-inch 4";
    if ([platform isEqualToString:@"iPad8,12"]) return @"iPad Pro 12.9-inch 4";
    
    if ([platform isEqualToString:@"iPad13,4"]) return @"iPad Pro 11-inch 3";
    if ([platform isEqualToString:@"iPad13,5"]) return @"iPad Pro 11-inch 3";
    if ([platform isEqualToString:@"iPad13,6"]) return @"iPad Pro 11-inch 3";
    if ([platform isEqualToString:@"iPad13,7"]) return @"iPad Pro 11-inch 3";
    
    if ([platform isEqualToString:@"iPad13,8"]) return @"iPad Pro 12.9-inch 5";
    if ([platform isEqualToString:@"iPad13,9"]) return @"iPad Pro 12.9-inch 5";
    if ([platform isEqualToString:@"iPad13,10"]) return @"iPad Pro 12.9-inch 5";
    if ([platform isEqualToString:@"iPad13,11"]) return @"iPad Pro 12.9-inch 5";

    if ([platform isEqualToString:@"iPad2,5"]) return @"iPad mini";
    if ([platform isEqualToString:@"iPad2,6"]) return @"iPad mini";
    if ([platform isEqualToString:@"iPad2,7"]) return @"iPad mini";
    
    if ([platform isEqualToString:@"iPad4,4"]) return @"iPad mini 2";
    if ([platform isEqualToString:@"iPad4,5"]) return @"iPad mini 2";
    if ([platform isEqualToString:@"iPad4,6"]) return @"iPad mini 2";
    
    if ([platform isEqualToString:@"iPad4,7"]) return @"iPad mini 3";
    if ([platform isEqualToString:@"iPad4,8"]) return @"iPad mini 3";
    if ([platform isEqualToString:@"iPad4,9"]) return @"iPad mini 3";

    if ([platform isEqualToString:@"iPad5,1"]) return @"iPad mini 4";
    if ([platform isEqualToString:@"iPad5,2"]) return @"iPad mini 4";
    
    if ([platform isEqualToString:@"iPad11,1"]) return @"iPad mini 5";
    if ([platform isEqualToString:@"iPad11,2"]) return @"iPad mini 5";
    
    if ([platform isEqualToString:@"iPad14,1"]) return @"iPad mini 6";
    if ([platform isEqualToString:@"iPad14,2"]) return @"iPad mini 6";
    
    /**iPod touch*/
    if ([platform isEqualToString:@"iPod1,1"]) return @"iPod touch";
    if ([platform isEqualToString:@"iPod2,1"]) return @"iPod touch 2";
    if ([platform isEqualToString:@"iPod3,1"]) return @"iPod touch 3";
    if ([platform isEqualToString:@"iPod4,1"]) return @"iPod touch 4";
    if ([platform isEqualToString:@"iPod5,1"]) return @"iPod touch 5";
    if ([platform isEqualToString:@"iPod7,1"]) return @"iPod touch 6";
    if ([platform isEqualToString:@"iPod9,1"]) return @"iPod touch 7";
    
    // 未知设备返回platform（方便后续查看日志）
    return platform;
}


#pragma mark - Alert

+ (void)showAlertWithTitle:(NSString *)title
           message:(NSString *)message
    viewController:(UIViewController *__weak)viewController
 cancelActionTitle:(NSString *)cancelActionTitle
confirmActionTitle:(NSString *)confirmActionTitle
        confirmActionBlock:(void(^)(UIAlertAction * _Nonnull action))confirmActionBlock{
    [self showAlertWithTitle:title message:message viewController:viewController cancelActionTitle:cancelActionTitle cancelActionStyle:UIAlertActionStyleCancel cancelActionBlock:nil confirmActionTitle:confirmActionTitle confirmActionStyle:UIAlertActionStyleDefault confirmActionBlock:confirmActionBlock];
}

+ (void)showAlertWithTitle:(NSString *)title
                   message:(NSString *)message
            viewController:(UIViewController *__weak)viewController
         cancelActionTitle:(NSString *)cancelActionTitle
         cancelActionStyle:(UIAlertActionStyle)cancelActionStyle
         cancelActionBlock:(nullable void (^)(UIAlertAction * _Nonnull))cancelActionBlock
        confirmActionTitle:(nullable NSString *)confirmActionTitle
        confirmActionStyle:(UIAlertActionStyle)confirmActionStyle
        confirmActionBlock:(nullable void (^)(UIAlertAction * _Nonnull))confirmActionBlock
{
    UIAlertController *alertController = [UIAlertController alertControllerWithTitle:title message:message preferredStyle:UIAlertControllerStyleAlert];
    
    if (![self checkStringUseable:cancelActionTitle]) { cancelActionTitle = PLVFDLocalizableString(@"取消"); }
    [alertController addAction:[UIAlertAction actionWithTitle:cancelActionTitle style:cancelActionStyle handler:^(UIAlertAction * _Nonnull action) {
        if (cancelActionBlock) { cancelActionBlock(action); }
    }]];
    
    if ([self checkStringUseable:confirmActionTitle]) {
        [alertController addAction:[UIAlertAction actionWithTitle:confirmActionTitle style:confirmActionStyle handler:^(UIAlertAction * _Nonnull action) {
            if (confirmActionBlock) { confirmActionBlock(action); }
        }]];
    }

    [viewController presentViewController:alertController animated:YES completion:nil];
}

@end
