//
//  PLVStickerEffectText.m
//  PolyvLiveScenesDemo
//
//  Created by polyv on 2025/7/9.
//  Copyright © 2025 PLV. All rights reserved.
//

#import "PLVStickerEffectText.h"
#import "PLVStickerEffectLable.h"
#import "PLVSAUtils.h"

@interface PLVStickerEffectText ()

@property (nonatomic, strong) PLVStickerEffectLable *effectLable;
@property (nonatomic, strong) UIImageView *bgImageView;
@property (nonatomic, strong) UIImageView *tipsIcon;

@property (nonatomic, strong) NSString *text;
@property (nonatomic, assign) PLVStickerTextTemplateType templateType;

@end

@implementation PLVStickerEffectText

- (instancetype)initWithText:(NSString *)text templateType:(PLVStickerTextTemplateType)templateType {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        self.text = text;
        self.templateType = templateType;

        [self setupUI];
    }
    return self;
}

- (void)layoutSubviews {
    [super layoutSubviews];

    switch (self.templateType) {
        case PLVStickerTextTemplateType0:
            // 关注主播 - 蓝色边框
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.layer.borderWidth = 2.0;
            self.effectLable.layer.borderColor = [UIColor colorWithRed:67/255.0 green:153/255.0 blue:255/255.0 alpha:1.0].CGColor;
            self.effectLable.layer.cornerRadius = 8.0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType1:
            // 限时抢购 - 红色边框
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.layer.borderWidth = 2.0;
            self.effectLable.layer.borderColor = [UIColor colorWithRed:255/255.0 green:69/255.0 blue:58/255.0 alpha:1.0].CGColor;
            self.effectLable.layer.cornerRadius = 8.0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType2:
            // 新品推荐 - 橙色背景
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.backgroundColor = [UIColor colorWithRed:255/255.0 green:149/255.0 blue:0/255.0 alpha:1.0];
            self.effectLable.layer.cornerRadius = 8.0;
            self.effectLable.layer.borderWidth = 0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType3:
            // 精品课程 - 黄绿色背景
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.backgroundColor = [UIColor colorWithRed:173/255.0 green:255/255.0 blue:47/255.0 alpha:1.0];
            self.effectLable.layer.cornerRadius = 8.0;
            self.effectLable.layer.borderWidth = 0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType4:
            // 分享有礼 - 粉色背景
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.backgroundColor = [UIColor colorWithRed:255/255.0 green:192/255.0 blue:203/255.0 alpha:1.0];
            self.effectLable.layer.cornerRadius = 8.0;
            self.effectLable.layer.borderWidth = 0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType5:
            // 精品课程 - 白色边框
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.layer.borderWidth = 2.0;
            self.effectLable.layer.borderColor = [UIColor whiteColor].CGColor;
            self.effectLable.layer.cornerRadius = 8.0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType6:
            // 扫码关注 - 蓝色条纹边框
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.layer.borderWidth = 0;
            self.effectLable.layer.cornerRadius = 8.0;
            
            // 添加虚线边框
            CAShapeLayer *dashedBorder = [CAShapeLayer layer];
            dashedBorder.strokeColor = [UIColor colorWithRed:67/255.0 green:153/255.0 blue:255/255.0 alpha:1.0].CGColor;
            dashedBorder.fillColor = [UIColor clearColor].CGColor;
            dashedBorder.lineDashPattern = @[@5, @3];
            dashedBorder.lineWidth = 2.0;
            dashedBorder.frame = self.bounds;
            dashedBorder.path = [UIBezierPath bezierPathWithRoundedRect:self.bounds cornerRadius:8.0].CGPath;
            [self.layer addSublayer:dashedBorder];
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
            
        case PLVStickerTextTemplateType7:
            // 看这里 - 红色圆点装饰
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = NO;
            self.tipsIcon.backgroundColor = [UIColor colorWithRed:255/255.0 green:69/255.0 blue:58/255.0 alpha:1.0];
            self.tipsIcon.layer.cornerRadius = 6.0;
            self.effectLable.layer.borderWidth = 0;
            
            self.bgImageView.frame = self.bounds;
            self.tipsIcon.frame = CGRectMake(self.bounds.size.width - 18, -6, 12, 12);
            self.effectLable.frame = self.bounds;
            break;
            
        default:
            self.bgImageView.hidden = YES;
            self.tipsIcon.hidden = YES;
            self.effectLable.layer.borderWidth = 0;
            
            self.bgImageView.frame = self.bounds;
            self.effectLable.frame = self.bounds;
            break;
    }

  
}

- (void)setupUI {
    self.bgImageView = [[UIImageView alloc] init];
    self.bgImageView.image = [UIImage imageNamed:@"plv_sticker_effect_text_bg"];
    [self addSubview:self.bgImageView];

    self.tipsIcon = [[UIImageView alloc] init];
    self.tipsIcon.image = [UIImage imageNamed:@"plv_sticker_effect_text_tips"];
    [self addSubview:self.tipsIcon];

    self.effectLable = [[PLVStickerEffectLable alloc] init];
    self.effectLable.text = self.text;
    [self configEffectLable:self.templateType];
    
    [self addSubview:self.effectLable];
}

- (void)updateText:(NSString *)text templateType:(PLVStickerTextTemplateType)templateType {
    self.text = text;
    self.effectLable.text = self.text;

    self.templateType = templateType;

    [self configEffectLable:templateType];
}

- (void)updateText:(NSString *)text{
    self.text = text;
    self.effectLable.text = self.text;
}

- (void)configEffectLable:(PLVStickerTextTemplateType )templateType{
    PLVStickerTextTemplate *template = [PLVStickerTextTemplate defaultTextTemplateWithTemplateType:templateType];
    self.effectLable.textColor = template.textColor;
    self.effectLable.backgroundColor = template.backgroundColor;
    self.effectLable.font = [UIFont fontWithName:template.fontName size:template.fontSize];
    self.effectLable.textAlignment = template.textAlignment;
    self.effectLable.strokeWidth = template.strokeWidth;
    self.effectLable.strokeColor = template.strokeColor;
    self.effectLable.textInsets = template.textInsets;
    self.effectLable.customShadowOffset = template.customShadowOffset;
    self.effectLable.customShadowBlurRadius = template.customShadowBlurRadius;
    self.effectLable.customShadowColor = template.customShadowColor;
}


@end
