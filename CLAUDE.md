# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

PolyvLiveScenes is an enterprise-grade multi-scene live streaming SDK platform developed by Polyv. It provides professional-quality live streaming solutions for education, e-commerce, and content creation verticals using a layered SDK architecture.

## Architecture & Structure

### Multi-Repository Architecture
This project uses a **polyrepo approach** with three separate SDK layers:

- **PLVFoundationSDK** (`PolyvFoundationSDK/`) - Base infrastructure (networking, auth, logging, utilities)
- **PLVBusinessSDK** (`PolyvBusinessSDK/`) - Core business logic (RTC abstraction, beauty effects, video player, screen sharing)  
- **PLVLiveScenesSDK** (`PolyvCloudClassSDK/`) - Scene-specific UI components and business logic

### Supported Live Streaming Scenes
1. **Cloud Classroom** - Educational streaming with interactive whiteboard, student management
2. **E-commerce Live Streaming** - Product-focused streaming with shopping cart integration
3. **Mobile Broadcasting** - Professional three-pane streaming interface for content creators
4. **Interactive Classroom** - Simplified single-view streaming for individual broadcasters

### Key Directories
- `PolyvLiveEcommerceDemo/` - Main demo applications and all scene implementations 
- `PolyvCloudClassSDK/framework_dyn/` - Pre-compiled framework files for distribution
- `docs/` - Architecture documentation and requirements

## Development Commands

### Environment Setup
```bash
# Install dependencies
cd PolyvLiveEcommerceDemo/PolyvLiveEcommerceDemo/
pod install

# Open workspace (always use .xcworkspace, never .xcodeproj)
open PolyvLiveScenesDemo.xcworkspace
```

### Building
```bash
# Clean build
xcodebuild clean -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvLiveScenesDemo

# Build individual SDK frameworks
xcodebuild -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvFoundationSDKBuild
xcodebuild -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvBusinessSDKBuild
xcodebuild -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvCloudClassSDKBuild

# Build all demo targets
xcodebuild -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvLiveScenesDemo -destination 'generic/platform=iOS'
xcodebuild -workspace PolyvLiveScenesDemo.xcworkspace -scheme PolyvLiveStreamerDemo -destination 'generic/platform=iOS'
```

### Testing
Currently uses manual testing through demo applications. No automated test runner configured.

### Linting
No specific linting configuration found. Follow iOS development best practices.

## Development Guidelines

### Coding Standards (from architecture docs)
- **Objective-C Naming**: Classes use `PLV` prefix (`PLVClassName`), methods use camelCase (`methodNameWithParameter:`)
- **Swift Naming**: Types use UpperCamelCase, variables/functions use lowerCamelCase
- **Memory Management**: Use ARC, avoid retain cycles with weak references
- **Thread Safety**: UI operations on main thread, network/database on background threads
- **Documentation**: All public interfaces require documentation comments

### Project Structure Conventions
- **File Organization**: Group by functional modules
- **Resource Management**: Use Assets.xcassets for images
- **Localization**: Use NSLocalizedString for user-visible strings
- **Error Handling**: Use NSError with clear error codes and descriptions

## Key Technical Integrations

### RTC Engines (Multi-engine support)
- Agora RTC Engine (primary) - v3.7.2.238
- Tencent Cloud RTC (backup) - v12.0.16292
- VolcEngine RTC (backup) - v3.41.304

### Beauty Effects
- PLVBytedEffectSDK - v4.4.2
- PLVBeautyEffect - v1.0.0

### Dependencies (CocoaPods)
- PLVIJKPlayer (video player) - v0.15.0
- SDWebImage (image loading) - v4.4.0
- MJRefresh (pull-to-refresh) - v3.5.0
- PLVAliHttpDNS (DNS optimization) - v3.2.0
- Multiple RTC engine SDKs

## Platform Requirements

- **iOS Version**: 12.0+ (optimized for iOS 15+)
- **CocoaPods**: 1.7.0+
- **Xcode**: 11.0+
- **Device Support**: iPhone 6s+, iPad Air 2+, iPod Touch 7th gen+

## Performance Requirements

- **Latency**: <200ms end-to-end for real-time interactions
- **Concurrency**: 10,000+ concurrent viewers per session
- **Memory**: <150MB additional memory footprint
- **Battery**: <10% additional battery consumption during active streaming

## Important Development Notes

- Always work with `.xcworkspace` files, never `.xcodeproj`
- Demo applications serve as comprehensive integration tests
- Framework architectures are verified automatically (x86_64, arm64)
- Multi-language support available through NSLocalizedString
- Security features include end-to-end encryption and token-based authentication
- GPU-accelerated video processing for beauty effects and real-time filters

## Common Workflows

### Adding New Features
1. Determine appropriate SDK layer (Foundation/Business/Scenes)
2. Follow existing architectural patterns in that layer
3. Update demo applications to showcase new functionality
4. Test across all supported RTC engines
5. Update API documentation

### Debugging Issues  
1. Enable comprehensive logging through PLVConsoleLogger
2. Use demo applications to reproduce issues
3. Check RTC engine abstraction layer for multi-engine issues
4. Verify framework architecture completeness

### Integration Support
- Complete demo applications with source code available
- Versioned API documentation at `https://repo.polyv.net/ios/documents/PLVLiveScenesSDK/{version}/index.html`
- TestFlight distribution for evaluation builds