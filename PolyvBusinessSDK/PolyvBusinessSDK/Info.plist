<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>1.26.0</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>NSPrincipalClass</key>
	<string></string>
	<key>PLVBusinessABSHSDK</key>
	<array>
		<string>PLVBroadcastAgoraSampleHandler</string>
	</array>
	<key>PLVBusinessARTCSDK</key>
	<array>
		<string>PLVBLinkMicAgoraManager</string>
	</array>
	<key>PLVBusinessBaseBSHSDK</key>
	<array>
		<string>PLVBroadcastSampleHandler</string>
	</array>
	<key>PLVBusinessBaseRTCSDK</key>
	<array>
		<string>PLVBLinkMicManager</string>
		<string>PLVBRTCConstants</string>
		<string>PLVBRTCVideoViewCanvasModel</string>
		<string>PLVBRTCVideoEncoderConfiguration</string>
		<string>PLVBRTCStatistics</string>
		<string>PLVMattingVideoProcessor</string>
	</array>
	<key>PLVBusinessBeautySDK</key>
	<array>
		<string>PLVBBeautyGPUPixelManager</string>
		<string>PLVBBeautyManager</string>
		<string>PLVBFilterOption</string>
		<string>PLVBBeautyBytedManager</string>
		<string>PLVBBytedHttpRequestProvider</string>
	</array>
	<key>PLVBusinessPlayerSDK</key>
	<array>
		<string>PLVBPublicStreamVOLCPlayer</string>
	</array>
	<key>PLVBusinessSDKSubModulesList</key>
	<array>
		<string>PLVBusinessARTCSDK</string>
		<string>PLVBusinessURTCSDK</string>
		<string>PLVBusinessTRTCSDK</string>
		<string>PLVBusinessVRTCSDK</string>
		<string>PLVBusinessBaseRTCSDK</string>
		<string>PLVBusinessSocketSDK</string>
		<string>PLVBusinessBaseBSHSDK</string>
		<string>PLVBusinessABSHSDK</string>
		<string>PLVBusinessUBSHSDK</string>
		<string>PLVBusinessTBSHSDK</string>
		<string>PLVBusinessVBSHSDK</string>
		<string>PLVBusinessBeautySDK</string>
		<string>PLVBusinessPlayerSDK</string>
	</array>
	<key>PLVBusinessSocketSDK</key>
	<array>
		<string>PLVBSocketClientConfiguration</string>
		<string>PLVBSocketClient</string>
	</array>
	<key>PLVBusinessTBSHSDK</key>
	<array>
		<string>PLVBroadcastTRTCSampleHandler</string>
	</array>
	<key>PLVBusinessTRTCSDK</key>
	<array>
		<string>PLVBLinkMicTRTCManager</string>
	</array>
	<key>PLVBusinessUBSHSDK</key>
	<array>
		<string>PLVBroadcastUCloudSampleHandler</string>
	</array>
	<key>PLVBusinessURTCSDK</key>
	<array>
		<string>PLVBLinkMicUCloudManager</string>
	</array>
	<key>PLVBusinessVBSHSDK</key>
	<array>
		<string>PLVBroadcastVOLCSampleHandler</string>
	</array>
	<key>PLVBusinessVRTCSDK</key>
	<array>
		<string>PLVBLinkMicVOLCManager</string>
	</array>
</dict>
</plist>
