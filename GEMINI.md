# Project Architecture Analysis

## Project Overview

The project is a mature, enterprise-grade live streaming SDK platform for iOS called PolyvLiveScenes. It's built with a layered polyrepo architecture, consisting of three main SDKs:

1.  **`PolyvFoundationSDK`**: The base layer providing fundamental services like networking, logging, utilities, and third-party integrations. It's written in Objective-C.
2.  **`PolyvBusinessSDK`**: The middle layer that handles core business logic, including a multi-engine RTC (Real-Time Communication) abstraction (supporting Agora, Tencent Cloud, and VolcEngine), media processing, GPU-accelerated beauty effects, and a custom video player. It's written in Objective-C and Swift.
3.  **`PolyvCloudClassSDK` (also referred to as `PLVLiveScenesSDK` in the docs):** The top layer that provides scene-specific UI components and business logic for four distinct streaming scenarios: cloud classroom, e-commerce streaming, professional broadcasting, and individual streaming. It's written in Swift and Objective-C.

## Key Architectural Features

*   **Polyrepo Structure:** The three SDKs are in separate repositories, allowing for independent development, versioning, and release cycles.
*   **Multi-Engine RTC:** An abstraction layer allows the application to switch between different RTC providers (Agora, Tencent, VolcEngine) at runtime for better reliability and regional performance.
*   **GPU-Accelerated Processing:** The SDK uses hardware acceleration for real-time video processing, particularly for beauty effects.
*   **Scene-Based Architecture:** The SDK is designed to support four different live streaming use cases, each with its own specialized UI and logic.
*   **Dependency Management:** The project uses CocoaPods for managing dependencies, including the various RTC engines and other third-party libraries.
*   **Demo Application:** The `PolyvLiveEcommerceDemo` is a comprehensive demo application that showcases how to integrate and use the SDKs for various live streaming scenarios.

## Technology Stack

*   **Languages:** Objective-C and Swift
*   **UI Framework:** UIKit
*   **Database:** SQLite for local data storage
*   **Networking:** Custom networking layer with WebSocket for real-time communication and REST for service communication.
*   **Dependencies:** The project uses a wide range of third-party libraries, including Agora, Tencent RTC, VolcEngine RTC, AliyunOSSiOS, Bugly, MJRefresh, SDWebImage, and many more, all managed via CocoaPods.

## Workflow

The typical workflow involves a client application integrating the SDKs via CocoaPods. The application initializes the SDK, configures the desired RTC engine, and then uses the scene-specific components to build the UI and handle user interactions. Real-time communication is handled through WebSockets and the selected RTC engine.
