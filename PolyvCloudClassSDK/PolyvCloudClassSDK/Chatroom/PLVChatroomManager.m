//
//  PLVChatroomManager.m
//  PLVLiveScenesSDK
//
//  Created by MissYasiky on 2020/11/24.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVChatroomManager.h"
#import "PLVLiveVideoConfig.h"
#import "PLVSocketManager.h"
#import "PLVLiveVideoAPI.h"
#import "PLVConsoleLogger.h"
#import "PLVWLogReporterManager.h"

#import <PLVFoundationSDK/PLVDataUtil.h>
#import <PLVFoundationSDK/PLVFdUtil.h>

@interface PLVChatroomManager ()<
PLVSocketManagerProtocol // socket协议
>

#pragma mark 数据
/// 当前频道号
@property (nonatomic, copy) NSString *channelId;

#pragma mark 聊天室状态
@property (nonatomic, assign) BOOL online;     /// socket处于已连接且登录成功的状态时为YES，默认为NO
@property (nonatomic, assign) BOOL closeRoom;  /// 聊天室是否被关闭，默认为NO
@property (nonatomic, assign) BOOL banned;     /// 当前登录用户是否被禁言，默认为NO

#pragma mark 点赞
@property (nonatomic, assign) NSInteger willSendSocketLikesCount; /// 即将通过socket发送的点赞数
@property (nonatomic, assign) NSInteger willSendHttpLikesCount;   /// 即将通过http接口发送的点赞数
@property (nonatomic, strong) NSTimer *likeTimer;                 /// 定时发送点赞数的5s计时器

@end

@implementation PLVChatroomManager {
    /// PLVSocketManager回调的执行队列
    dispatch_queue_t socketDelegateQueue;
}

#pragma mark - 生命周期

+ (instancetype)sharedManager {
    static dispatch_once_t onceToken;
    static PLVChatroomManager *mananger = nil;
    dispatch_once(&onceToken, ^{
        mananger = [[self alloc] init];
    });
    return mananger;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        socketDelegateQueue = dispatch_get_global_queue(0, DISPATCH_QUEUE_PRIORITY_DEFAULT);
    }
    return self;
}

- (void)setupWithDelegate:(id<PLVChatroomManagerProtocol>)delegate channelId:(NSString *)channelId {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeChatRoom, @"setup delegate with (delegate:%@, channelId:%@)", delegate, channelId);
    if (delegate) {
        self.delegate = delegate;
    }
    
    if (channelId && [channelId isKindOfClass:[NSString class]] && channelId.length > 0) {
        self.channelId = channelId;
    }
    
    // 监听socket消息
    PLVSocketManager *socketManager = [PLVSocketManager sharedManager];
    [socketManager addDelegate:self delegateQueue:socketDelegateQueue];
    
    if (socketManager.login) {
        self.online = YES;
        
        NSString *ackString = socketManager.loginSuccessAckString;
        if (ackString && [ackString isKindOfClass:[NSString class]] && ackString.length > 4) {
            int status = [[socketManager.loginSuccessAckString substringWithRange:NSMakeRange(4, 1)] intValue];
            self.banned = (status == 0);
        }
    }
    
    // 点赞发送计时器
    self.likeTimer = [NSTimer scheduledTimerWithTimeInterval:5.0 target:self selector:@selector(likeCountTimerAction) userInfo:nil repeats:YES];
}

- (void)destroy {
    PLV_LOG_INFO(PLVConsoleLogModuleTypeChatRoom, @"%s", __FUNCTION__);
    [self.likeTimer invalidate];
    self.likeTimer = nil;
    
    self.specialRole = NO;
    self.online = NO;
    self.closeRoom = NO;
    self.banned = NO;
    self.sessionId = nil;
    self.channelId = nil;
    
    self.willSendSocketLikesCount = 0;
    self.willSendHttpLikesCount = 0;
}

#pragma mark - Getter & Setter

- (void)setSpecialRole:(BOOL)specialRole {
    _specialRole = specialRole;
    if (specialRole) {
        self.banned = NO;
    }
}

- (BOOL)online {
    return [PLVSocketManager sharedManager].login && [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected;
}

#pragma mark - 发送消息

#pragma mark 推流特有消息

- (BOOL)sendBandMessage:(BOOL)banned bannedUserId:(NSString *)userId {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send band message failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    
    if (!userId || ![userId isKindOfClass:[NSString class]] || userId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send band message failed with 【param illegal】(userId:%@)", __FUNCTION__, userId);
        return NO;
    }
    
    if (!self.specialRole) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s sendBandMessage failed with 【权限不足】(specialRole:%d)", __FUNCTION__, self.specialRole);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    
    NSString *event = banned ? @"SHIELD" : @"REMOVE_SHIELD";
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    plv_dict_set(dict, @"EVENT", event);
    plv_dict_set(dict, @"roomId", roomId);
    plv_dict_set(dict, @"channelId", self.channelId);
    plv_dict_set(dict, @"value", userId);
    plv_dict_set(dict, @"type", @"userId");
    NSString *sign = [NSString stringWithFormat:@"polyv_room_sign%@", self.channelId];
    plv_dict_set(dict, @"sign", [PLVDataUtil md5HexDigest:sign]);
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:dict];
    return success;
}

- (BOOL)sendKickMessageWithUserId:(NSString *)userId {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send kick message failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    
    if (!userId || ![userId isKindOfClass:[NSString class]] || userId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send kick message failed with 【param illegal】(userId:%@)", __FUNCTION__, userId);
        return NO;
    }
    
    if (!self.specialRole) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s sendKickMessage failed with 【权限不足】(specialRole:%d)", __FUNCTION__, self.specialRole);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    plv_dict_set(dict, @"EVENT", @"KICK");
    plv_dict_set(dict, @"roomId", roomId);
    plv_dict_set(dict, @"channelId", self.channelId);
    plv_dict_set(dict, @"userId", userId);
    NSString *sign = [NSString stringWithFormat:@"polyv_room_sign%@", self.channelId];
    plv_dict_set(dict, @"sign", [PLVDataUtil md5HexDigest:sign]);
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:dict];
    return success;
}

- (BOOL)sendUnkickMessageWithUserId:(NSString *)userId {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send unkick message failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    
    if (!userId || ![userId isKindOfClass:[NSString class]] || userId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send unkick message failed with 【param illegal】(userId:%@)", __FUNCTION__, userId);
        return NO;
    }
    
    if (!self.specialRole) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s sendUnkickMessage failed with 【权限不足】(specialRole:%@)", __FUNCTION__, self.specialRole);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    plv_dict_set(dict, @"EVENT", @"UNKICK");
    plv_dict_set(dict, @"type", @"userId");
    plv_dict_set(dict, @"roomId", roomId);
    plv_dict_set(dict, @"channelId", self.channelId);
    plv_dict_set(dict, @"value", userId);
    NSString *sign = [NSString stringWithFormat:@"polyv_room_sign%@", self.channelId];
    plv_dict_set(dict, @"sign", [PLVDataUtil md5HexDigest:sign]);
    [[PLVSocketManager sharedManager] emitMessage:dict];
    return YES;
}

- (BOOL)sendCloseRoom:(BOOL)closeRoom {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s sendCloseRoom failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    if (!self.specialRole) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s sendCloseRoom failed with 【权限不足】(specialRole:%d)", __FUNCTION__, self.specialRole);
        return NO;
    }
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    NSString *sign = [NSString stringWithFormat:@"polyv_room_sign%@", self.channelId];
    
    plv_dict_set(dict, @"EVENT", @"CLOSEROOM");
    plv_dict_set(dict, @"roomId", roomId);
    plv_dict_set(dict, @"sign", [PLVDataUtil md5HexDigest:sign]);
    
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:dict];
    return success;
}

#pragma mark 提问消息

- (BOOL)sendQuesstionMessage:(NSString *)content {
    if (!self.online || !self.channelId) { // 提问消息属于私聊，不受聊天室关闭、用户禁言的影响
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send quesstiom message failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    if (!content || ![content isKindOfClass:NSString.class] || content.length == 0 ||
        !roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send quesstiom message failed with 【param illegal】(content:%@, roomId:%@)", __FUNCTION__, content, roomId);
        return NO;
    }
    
    NSString *userId = [PLVSocketManager sharedManager].viewerId ?: @"";
    NSString *userName = [PLVSocketManager sharedManager].viewerName ?: @"";
    NSString *userAvatar = [PLVSocketManager sharedManager].avatarUrl ?: @"";
    NSString *userType = [PLVSocketManager sharedManager].userTypeString ?: @"";
    NSString *actor = PLVFDLocalizableString(@"学生");
    
    NSMutableDictionary *speakDict = [NSMutableDictionary dictionary];
    plv_dict_set(speakDict, @"EVENT", @"S_QUESTION");
    plv_dict_set(speakDict, @"content", content);
    plv_dict_set(speakDict, @"roomId", roomId);
    
    NSDictionary *user = @{
                            @"nick" : userName,
                            @"actor" : actor,
                            @"pic" : userAvatar,
                            @"userId" : userId,
                            @"userType" : userType
                          };
    plv_dict_set(speakDict, @"user", user);
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:speakDict];
    return success;
}

#pragma mark 自定义消息

- (BOOL)sendCustonMessage:(PLVCustomMessage *)message {
    return [self sendCustonMessage:message joinHistoryList:YES];
}

- (BOOL)sendCustonMessage:(PLVCustomMessage *)message
          joinHistoryList:(BOOL)joinHistoryList {
    if (!self.online || !self.channelId ||
        (!self.specialRole && self.closeRoom)) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send custom message failed with 【param illegal】(online:%d, channelId:%@, closeRoom:%d, banned:%d)", __FUNCTION__, self.online, self.channelId, self.closeRoom, self.banned);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    NSString *event = message.event;
    NSString *tip = message.tip;
    if (!message || !message.data ||
        !event || ![event isKindOfClass:[NSString class]] || event.length == 0 || event.length > 20 ||
        !tip || ![tip isKindOfClass:[NSString class]] || tip.length == 0 ||
        !roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send custom message failed with 【param illegal】(message:%@, event:%@, tip:%@, roomId:%@)", __FUNCTION__, message, event, tip, roomId);
        return NO;
    }
    
    NSMutableDictionary *customMsgDict = [NSMutableDictionary dictionary];
    plv_dict_set(customMsgDict, @"EVENT", event);
    plv_dict_set(customMsgDict, @"version", @(1));
    plv_dict_set(customMsgDict, @"tip", tip);
    plv_dict_set(customMsgDict, @"data", message.data);
    plv_dict_set(customMsgDict, @"emitMode", @(message.emitMode));
    plv_dict_set(customMsgDict, @"roomId", roomId);
    plv_dict_set(customMsgDict, @"joinHistoryList", @(joinHistoryList));

    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"customMessage" content:customMsgDict];
    return success;
}

#pragma mark 文本消息

- (BOOL)sendSpeakMessage:(PLVSpeakMessage *)message {
    return [self sendSpeakMessage:message callback:nil];
}

- (BOOL)sendSpeakMessage:(PLVSpeakMessage *)message
                callback:(void (^ _Nullable)(NSString *msgId))callback {
    return [self sendMessageContent:message.content replyMsgId:nil source:message.source callback:^(NSArray *ackArray) {
        NSString *msgId = nil;
        NSDictionary *extraDict = nil;
        if (ackArray) {
            msgId = [ackArray count] > 0 ? ackArray[0] : nil;
            extraDict = [ackArray count] > 1 ? ackArray[1] : nil;
        }
        if (extraDict && [extraDict isKindOfClass:[NSDictionary class]]) {
            NSString *content = extraDict[@"content"] ?: nil;
            if (content && [content isKindOfClass:[NSString class]] && content.length > 0) {
                message.content = content;
                message.prohibitWordReplaced = YES;
            }
        }
        if (msgId && [msgId isKindOfClass:[NSString class]] && msgId.length > 0) {
            message.msgId = msgId;
            !callback ?: callback(msgId);
        }
    }];
}

- (BOOL)sendQuoteMessage:(PLVQuoteMessage *)message {
    return [self sendQuoteMessage:message callback:nil];
}

- (BOOL)sendQuoteMessage:(PLVQuoteMessage *)message
                callback:(void (^ _Nullable)(NSString *msgId))callback {
    return [self sendMessageContent:message.content replyMsgId:message.quoteMsgId callback:^(NSArray *ackArray) {
        NSString *msgId = nil;
        NSDictionary *extraDict = nil;
        if (ackArray) {
            msgId = [ackArray count] > 0 ? ackArray[0] : nil;
            extraDict = [ackArray count] > 1 ? ackArray[1] : nil;
        }
        if (extraDict && [extraDict isKindOfClass:[NSDictionary class]]) {
            NSString *content = extraDict[@"content"] ?: nil;
            if (content && [content isKindOfClass:[NSString class]] && content.length > 0) {
                message.content = content;
                message.prohibitWordReplaced = YES;
            }
        }
        if (msgId && [msgId isKindOfClass:[NSString class]] && msgId.length > 0) {
            message.msgId = msgId;
            !callback ?: callback(msgId);
        }
    }];
}



- (BOOL)sendMessageContent:(NSString *)content
                replyMsgId:(NSString * _Nullable)replyMsgId
                  callback:(void (^)(NSArray *ackArray))callback {
    return [self sendMessageContent:content replyMsgId:replyMsgId source:nil callback:callback];
}

- (BOOL)sendMessageContent:(NSString *)content
                replyMsgId:(NSString * _Nullable)replyMsgId
                    source:(NSString * _Nullable)source
                  callback:(void (^)(NSArray *ackArray))callback {
    if (!self.online || !self.channelId ||
        (!self.specialRole && (self.closeRoom || self.banned))) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send speak message failed with 【param illegal】(online:%d, closeRoom:%d, banned:%d, channelId:%@)", __FUNCTION__, self.online, self.closeRoom, self.banned, self.channelId);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    NSString *accountId = [PLVLiveVideoConfig sharedInstance].userId; // 在互动学堂中此属性无法获取，不是必须参数
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }

    if (![PLVFdUtil checkStringUseable:content] ||
        ![PLVFdUtil checkStringUseable:roomId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send message failed with 【param illegal】(content:%@, roomId:%@)", __FUNCTION__, content, roomId);
        return NO;
    }
    
    NSMutableDictionary *speakDict = [NSMutableDictionary dictionary];
    plv_dict_set(speakDict, @"EVENT", @"SPEAK");
    plv_dict_set(speakDict, @"values", @[content]);
    plv_dict_set(speakDict, @"needIdCallback", @(YES));
    plv_dict_set(speakDict, @"roomId", roomId);
    plv_dict_set(speakDict, @"channelId", self.channelId);
    plv_dict_set(speakDict, @"accountId", accountId);
    if (self.sessionId && [self.sessionId isKindOfClass:[NSString class]] && self.sessionId.length > 0) {
        plv_dict_set(speakDict, @"sessionId", self.sessionId);
    }
    if (replyMsgId && [replyMsgId isKindOfClass:[NSString class]] && replyMsgId.length > 0) {
        plv_dict_set(speakDict, @"quoteId", replyMsgId);
    }
    if ([PLVFdUtil checkStringUseable:source]) {
        plv_dict_set(speakDict, @"source", source);
    }
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:speakDict timeout:0 callback:callback];
    return success;
}

#pragma mark 图片消息
- (void)sendImageMessage:(PLVImageMessage *)message {
    if (!self.online || !self.channelId ||
        (!self.specialRole && (self.closeRoom || self.banned))) {
        [self sendImageMessageFailed:message];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send image message failed with 【param illegal】(online:%d, channelId:%@, closeRoom:%d, banned:%d)", __FUNCTION__, self.online, self.channelId, self.closeRoom, self.banned);
        return;
    }
    
    NSString *imageName = message.imageName;
    NSString *imageId = message.imageId;
    if (!message ||
        (!message.image && !message.processImageData) ||
        !imageName || ![imageName isKindOfClass:[NSString class]] || imageName.length == 0 ||
        !imageId || ![imageId isKindOfClass:[NSString class]] || imageId.length == 0) {
        [self sendImageMessageFailed:message];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send image message failed with 【param illegal】(message:%@)", __FUNCTION__, message);
        return;
    }
    
    message.uploadProgress = 0;
    message.uploadState = PLVImageUploadStateReady;
    message.sendState = PLVImageMessageSendStateReady;
        
    __weak typeof(self) weakSelf = self;
    
    // 上传进度发生变化回调
    void(^progressBlock)(float fractionCompleted) = ^(float fractionCompleted) {
        [weakSelf uploadImage:message updateProgress:fractionCompleted];
    };
    
    // 上传成功回调
    void(^successBlock)(NSDictionary *uploadImageTokenDict, NSString *key, NSString *imageName) = ^(NSDictionary *uploadImageTokenDict, NSString *key, NSString *imageName) {
        message.uploadProgress = 1;
        message.uploadState = PLVImageUploadStateSuccess;
        
        NSString *host = PLV_SafeStringForDictKey(uploadImageTokenDict, @"host") ?: @"";
        NSString *imageUrl = [NSString stringWithFormat:@"https://%@/%@", host, (key ?: @"")];
        message.imageUrl = imageUrl;
        BOOL success = [weakSelf emitImageMessage:message imageUrl:imageUrl];
        if (!success) {
            [weakSelf sendImageMessageFailed:message];
        }
    };
    
    // 上传失败回调
    void(^failureBlock)(NSError *error) = ^(NSError *error) {
        message.uploadState = PLVImageUploadStateFailed;
        [weakSelf sendImageMessageFailed:message];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s upload image failed with 【%@】", __FUNCTION__ ,error);
    };
    
    if (message.processImageData &&
        message.processImageData.length <= 2 * 1024 * 1024) {
        [PLVLiveVideoAPI uploadImageData:message.processImageData imageName:imageName channelId:self.channelId progress:progressBlock success:successBlock fail:failureBlock];
    } else {
        [PLVLiveVideoAPI uploadImage:message.image imageName:imageName channelId:self.channelId progress:progressBlock success:successBlock fail:failureBlock];
    }
}

- (BOOL)emitImageMessage:(PLVImageMessage *)message imageUrl:(NSString *)imageUrl {
    if (!self.online || !self.channelId ||
        (!self.specialRole && (self.closeRoom || self.banned))) {
        [self sendImageMessageFailed:message];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s emit image message failed with 【param illegal】(online:%d, channelId:%@, closeRoom:%d, banned:%d)", __FUNCTION__, self.online, self.channelId, self.closeRoom, self.banned);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    NSString *accountId = [PLVLiveVideoConfig sharedInstance].userId; // 在互动学堂中此属性无法获取，不是必须参数
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        [self sendImageMessageFailed:message];
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send speak message failed with 【param illegal】(roomId:%@)", __FUNCTION__, roomId);
        return NO;
    }
    
    UIImage *image = message.image;
    NSString *imageId = message.imageId;
    NSString *source = message.source;
    
    NSMutableDictionary *speakDict = [NSMutableDictionary dictionary];
    plv_dict_set(speakDict, @"EVENT", @"CHAT_IMG");
    plv_dict_set(speakDict, @"roomId", roomId);
    plv_dict_set(speakDict, @"channelId", self.channelId);
    plv_dict_set(speakDict, @"needIdCallback", @(YES));
    plv_dict_set(speakDict, @"accountId", accountId);
    if (self.sessionId && [self.sessionId isKindOfClass:[NSString class]] && self.sessionId.length > 0) {
        plv_dict_set(speakDict, @"sessionId", self.sessionId);
    }
    if ([PLVFdUtil checkStringUseable:source]) {
        plv_dict_set(speakDict, @"source", source);
    }
    
    NSArray *values = @[@{
                            @"type":@"chatImg",
                            @"msgSource":@"chatImg",
                            @"status":@"upLoadingSuccess",
                            @"msg":PLVFDLocalizableString(@"上传图片成功"),
                            @"id":imageId,
                            @"uploadImgUrl": imageUrl,
                            @"size":@{@"width":@(image.size.width),
                                      @"height":@(image.size.height)}
    }];
    plv_dict_set(speakDict, @"values", values);
    
    __weak typeof(self) weakSelf = self;
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:speakDict timeout:0 callback:^(NSArray * _Nonnull ackArray) {
        NSString *msgId = nil;
        if (ackArray && [ackArray count] > 0) {
            msgId = ackArray[0];
        }
        if (msgId && [msgId isKindOfClass:[NSString class]] && msgId.length > 0) {
            message.msgId = msgId;
            [weakSelf sendImageMessageSuccess:message];
        }
    }];
    if (!success) {
        [weakSelf sendImageMessageFailed:message];
    }
    return success;
}

- (void)uploadImage:(PLVImageMessage *)message updateProgress:(CGFloat)progress {
    message.uploadProgress = progress;
    message.uploadState = PLVImageUploadStateUploading;
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(chatroomManager_sendImageMessage:updateProgress:)]) {
        [self.delegate chatroomManager_sendImageMessage:message updateProgress:progress];
    }
}

- (void)sendImageMessageFailed:(PLVImageMessage *)message {
    message.sendState = PLVImageMessageSendStateFailed;
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(chatroomManager_sendImageMessageFaild:)]) {
        [self.delegate chatroomManager_sendImageMessageFaild:message];
    }
}

- (void)sendImageMessageSuccess:(PLVImageMessage *)message {
    message.sendState = PLVImageMessageSendStateSuccess;
    if (self.delegate &&
        [self.delegate respondsToSelector:@selector(chatroomManager_sendImageMessageSuccess:)]) {
        [self.delegate chatroomManager_sendImageMessageSuccess:message];
    }
}

#pragma mark 图片表情消息

- (BOOL)sendImageEmotionMessage:(PLVImageEmotionMessage *)message {
    if (!self.online || !self.channelId ||
        (!self.specialRole && (self.closeRoom || self.banned))) {
        message.sendState = PLVImageEmotionMessageSendStateFailed;
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s emit image emotion message failed with 【param illegal】(online:%d, channelId:%@, closeRoom:%d, banned:%d)", __FUNCTION__, self.online, self.channelId, self.closeRoom, self.banned);
        return NO;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    NSString *accountId = [PLVLiveVideoConfig sharedInstance].userId;// 在互动学堂中此属性无法获取，不是必须参数
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        message.sendState = PLVImageEmotionMessageSendStateFailed;
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send image emotion message failed with 【param illegal】(roomId:%@)", __FUNCTION__,  roomId);
        return NO;
    }

    NSMutableDictionary *emotionDict = [NSMutableDictionary dictionary];
    plv_dict_set(emotionDict, @"roomId", roomId);
    plv_dict_set(emotionDict, @"channelId", self.channelId);
    plv_dict_set(emotionDict, @"needIdCallback", @(YES));
    plv_dict_set(emotionDict, @"accountId", accountId);
    plv_dict_set(emotionDict, @"id", message.imageId);
    //当为特殊身份聊天
    if (self.specialRole) {
        plv_dict_set(emotionDict, @"source", @"extend");
    }

    if (self.sessionId && [self.sessionId isKindOfClass:[NSString class]] && self.sessionId.length > 0) {
        plv_dict_set(emotionDict, @"sessionId", self.sessionId);
    }
    
    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"emotion" content:emotionDict timeout:0 callback:^(NSArray * _Nonnull ackArray) {
        NSString *jsonString = (ackArray && [ackArray count] > 0) ? ackArray[0] : nil;
        if (jsonString &&
            [jsonString isKindOfClass:[NSString class]] &&
            jsonString.length > 0) {
            NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
            NSError *err;
            NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                options:NSJSONReadingMutableContainers
                                                                  error:&err];
            if (err || !PLV_SafeDictionaryForValue(dict)) {
                message.sendState = PLVImageEmotionMessageSendStateFailed;
            } else {
                NSInteger code = PLV_SafeIntegerForDictKey(dict, @"code");
                NSDictionary *dictData = PLV_SafeDictionaryForDictKey(dict, @"data");
                if (code == 200 && dictData) {
                    NSString *msgId = dictData[@"messageId"];
                    if (msgId && [msgId isKindOfClass:[NSString class]] && msgId.length > 0) {
                        message.sendState = PLVImageEmotionMessageSendStateSuccess;
                        message.msgId = msgId;
                    } else {
                        message.sendState = PLVImageEmotionMessageSendStateFailed;
                    }
                } else {
                    message.sendState = PLVImageEmotionMessageSendStateFailed;
                }
            }
        }
      }];
    if (!success) {
        message.sendState = PLVImageEmotionMessageSendStateFailed;
    }
    return success;
}

#pragma mark - 点赞

- (void)sendLikeEvent {
    [self sendLikeEvent:1];
}

- (void)sendLikeEvent:(NSInteger)count {
    if (self.willSendSocketLikesCount > 200 || self.willSendHttpLikesCount > 200 || count < 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send like failed with 【param illegal】(willSendSocketLikesCount:%zd, willSendHttpLikesCount:%zd, count:%zd)", __FUNCTION__, self.willSendSocketLikesCount, self.willSendHttpLikesCount, count);
        return;
    }
    self.willSendSocketLikesCount += count;
    self.willSendHttpLikesCount += count;
}

- (void)likeCountTimerAction {
    // 每5秒发送一次点赞Socket
    NSInteger socketLikeCount = MIN(200, self.willSendSocketLikesCount);
    if (socketLikeCount > 0) { // 发送socket消息
        [self emitLikeCount:socketLikeCount];
    }
    
    // 每5秒发送一次点赞http统计
    NSInteger httpLikeCount = MIN(200, self.willSendHttpLikesCount);
    if (httpLikeCount > 0) { // 发送http消息
        [self reportLikeCount:httpLikeCount];
    }
}

- (void)emitLikeCount:(NSInteger)likeCount {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s emit like count failed with 【param illegal】(online:%d, closeRoom:%d, banned:%d, channelId:%@)", __FUNCTION__, self.online, self.closeRoom, self.banned, self.channelId);
        return;
    }
    
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    NSString *userId = [PLVSocketManager sharedManager].viewerId;
    NSString *userName = [PLVSocketManager sharedManager].viewerName;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0 ||
        !userId || ![userId isKindOfClass:[NSString class]] || userId.length == 0 ||
        !userName || ![userName isKindOfClass:[NSString class]] || userName.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s emit like count failed with 【param illegal】(roomId:%@, userId:%@, userName:%@)", __FUNCTION__, roomId, userId, userName);
        return;
    }
    
    NSMutableDictionary *likeDict = [NSMutableDictionary dictionary];
    plv_dict_set(likeDict, @"EVENT", @"LIKES");
    plv_dict_set(likeDict, @"count", @(likeCount));
    plv_dict_set(likeDict, @"userId", userId);
    plv_dict_set(likeDict, @"nick", userName);
    plv_dict_set(likeDict, @"roomId", roomId);
    if (self.sessionId && [roomId isKindOfClass:[NSString class]] && roomId.length > 0) {
        plv_dict_set(likeDict, @"sessionId", self.sessionId);
    }
    
    [[PLVSocketManager sharedManager] emitMessage:likeDict];
    self.willSendSocketLikesCount -= likeCount;
    
    NSDictionary *eventInfo = @{
        @"clickTime": @([PLVFdUtil curTimeInterval]),
        @"clickNumber" : @(likeCount)
    };
    [[PLVWLogReporterManager sharedManager] reportTrackWithEventId:@"user_click_live" eventType:@"click" specInformation:eventInfo];
}

- (void)reportLikeCount:(NSInteger)likeCount {
    NSString *userId = [PLVSocketManager sharedManager].viewerId;
    
    __weak typeof(self) weakSelf = self;
    [PLVLiveVideoAPI likeWithChannelId:self.channelId.integerValue viewerId:userId times:likeCount completion:^{
        weakSelf.willSendHttpLikesCount -= likeCount;
    } failure:^(NSError * _Nonnull error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s report like count failed with 【%@】", __FUNCTION__, error);
    }];
}

#pragma mark - 修改昵称消息

- (BOOL)sendChangeNickname:(NSString *)nickname {
    if (!self.online || !self.channelId) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send change nickname message failed with 【param illegal】(online:%d, channelId:%@)", __FUNCTION__, self.online, self.channelId);
        return NO;
    }
    if (!nickname || ![nickname isKindOfClass:[NSString class]] || nickname.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send change nickname message failed with 【param illegal】(nickname:%@)", __FUNCTION__, nickname);
        return NO;
    }
    NSString *roomId = [PLVSocketManager sharedManager].roomId;
    if (!roomId || ![roomId isKindOfClass:[NSString class]] || roomId.length == 0) {
        roomId = self.channelId;
    }
    
    NSString *userId = [PLVSocketManager sharedManager].viewerId ?: @"";
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    plv_dict_set(dict, @"EVENT", @"SET_NICK");
    plv_dict_set(dict, @"roomId", roomId);
    plv_dict_set(dict, @"channelId", self.channelId);
    plv_dict_set(dict, @"nick", nickname);
    plv_dict_set(dict, @"userId", userId);
    BOOL success = [[PLVSocketManager sharedManager] emitMessage:dict];
    return success;
}

- (BOOL)sendPinMessageWithMsgId:(NSString *_Nullable)msgId toTop:(BOOL)toTop {
    if (!self.online) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send pin message failed with 【param illegal】(online:%d)", __FUNCTION__, self.online);
        return NO;
    }
    if (!msgId || ![msgId isKindOfClass:[NSString class]] || msgId.length == 0) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s send %@  message failed with 【param illegal】(msgId:%@)", __FUNCTION__, toTop ? @"to top" : @"cancel top", msgId);
        return NO;
    }
    
    NSDictionary *dict = @{@"EVENT" : @"CANCEL_TOP", @"id": msgId};
    if (toTop) {
        dict = @{@"EVENT": @"TO_TOP", @"id": msgId};
    }
    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"speak" content:dict];
    return success;
}

#pragma mark - 获取超长消息

- (BOOL)overLengthSpeakMessageWithMsgId:(NSString *)msgId callback:(void (^)(NSString *content))callback {
    if (![PLVFdUtil checkStringUseable:msgId]) {
        return NO;
    }
    NSDictionary *contentDict = @{ @"EVENT" : @"GET_FULL_MESSAGE", @"id" : msgId };
    BOOL emit = [[PLVSocketManager sharedManager] emitEvent:@"speak" content:contentDict timeout:5.0 callback:^(NSArray *ackArray) {
        NSString *content = nil;
        id jsonObject = nil;
        if ([PLVFdUtil checkArrayUseable:ackArray]) {
            if ([ackArray.firstObject isKindOfClass:[NSString class]]) {
                NSData *jsonData = [ackArray.firstObject dataUsingEncoding:NSUTF8StringEncoding];
                if (jsonData && jsonData.length > 0) {
                    jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingAllowFragments error:nil];
                }
            } else if ([ackArray.firstObject isKindOfClass:[NSDictionary class]]) {
                jsonObject = ackArray.firstObject;
            }
            
            NSString *status = PLV_SafeStringForDictKey(jsonObject, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *dataDict = PLV_SafeDictionaryForDictKey(jsonObject, @"data");
                content = PLV_SafeStringForDictKey(dataDict, @"content");
            }
        }
        callback(content);
    }];
    return emit;
}

#pragma mark - PLVSocketManagerProtocol

- (void)socketMananger_didReceiveMessage:(NSString *)subEvent
                                    json:(NSString *)jsonString
                              jsonObject:(id)object {
    NSDictionary *jsonDict = (NSDictionary *)object;
    if (![jsonDict isKindOfClass:[NSDictionary class]]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeChatRoom, @"%s socket mananger did receive message failed with 【param illegal】(jsonDict:%@)", __FUNCTION__, jsonDict);
        return;
    }
    
    if ([subEvent isEqualToString:@"CLOSEROOM"]) { // admin closes or opens the chatroom
        
        NSDictionary *value = PLV_SafeDictionaryForDictKey(jsonDict, @"value");
        self.closeRoom = PLV_SafeBoolForDictKey(value, @"closed");
        
    } else if ([subEvent isEqualToString:@"ADD_SHIELD"] ||
               [subEvent isEqualToString:@"REMOVE_SHIELD"]) {
    
        NSString *shieldUserId = PLV_SafeStringForDictKey(jsonDict, @"value");
        NSString *loginUserId = [PLVSocketManager sharedManager].viewerId;
        if (!self.specialRole && shieldUserId && [shieldUserId isEqualToString:loginUserId]) {
            self.banned = [subEvent isEqualToString:@"ADD_SHIELD"];
        }
    
    } else if ([subEvent isEqualToString:@"SPEAK"]) {
        NSString *status = PLV_SafeStringForDictKey(jsonDict, @"status");
        if (status) { // 单播消息
            if ([status isEqualToString:@"censor"]) { // 聊天审核
            } else if ([status isEqualToString:@"error"]) { // 严禁词
                if (self.delegate &&
                    [self.delegate respondsToSelector:@selector(chatroomManager_receiveWarning:prohibitWord:)]) {
                    NSString *message = PLV_SafeStringForDictKey(jsonDict, @"message");
                    NSString *word = PLV_SafeStringForDictKey(jsonDict, @"value");
                    [self.delegate chatroomManager_receiveWarning:message prohibitWord:word];
                }
            }
        }
    } else if ([subEvent isEqualToString:@"CHAT_IMG"]) {
        if (!jsonDict ||
            !jsonDict.allKeys ||
            ![jsonDict.allKeys containsObject:@"result"] ||
            ![jsonDict valueForKey:@"result"]) {
            return;
        }
        BOOL result = [[jsonDict valueForKey:@"result"] boolValue];
        if (!result) {//图片内容违规，单播消息
            if (self.delegate &&
                [self.delegate respondsToSelector:@selector(chatroomManager_receiveImageWarningWithMsgId:)]) {
                NSString *msgId = PLV_SafeStringForDictKey(jsonDict, @"id");
                [self.delegate chatroomManager_receiveImageWarningWithMsgId:msgId];
            }
        }
    }
}

- (void)socketMananger_didLoginSuccess:(NSString *)ackString {
    self.online = YES;
    
    if (ackString && [ackString isKindOfClass:[NSString class]] && ackString.length > 4) {
        int status = [[ackString substringWithRange:NSMakeRange(4, 1)] intValue];
        if (!self.specialRole) {
            self.banned = (status == 0);
        }
    }
}

- (void)socketMananger_didLoginFailure:(NSError *)error {
    self.online = NO;
}

@end
