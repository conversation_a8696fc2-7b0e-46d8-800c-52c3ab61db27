//
//  PLVRTCStreamerManager.m
//  PLVCloudClassStreamerDemo
//
//  Created by <PERSON><PERSON> on 2019/10/21.
//  Copyright © 2019 plv. All rights reserved.
//

#import "PLVRTCStreamerManager.h"

#import "PLVSocketManager.h"
#import "PLVLiveVideoAPI.h"
#import "PLVLivePrivateAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVConsoleLogger.h"
#import "PLVBeautyManager+Private.h"
#import "PLVBeautyResourceManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import "PLVLiveVideoConfig+PrivateInfo.h"

#import <PLVFoundationSDK/PLVFoundationSDK.h>

static NSString *const PLVRTCStreamerManager_MixAction_Start = @"start";
static NSString *const PLVRTCStreamerManager_MixAction_Update = @"update";
static NSString *const PLVRTCStreamerManager_MixAction_End = @"end";
static NSString *const kPLVRTCStreamerManager_SIP_GuestPic = @"https://liveimages.videocc.net/defaultImg/avatar/viewer.png";

#define MaxCameraZoomRatio 5    // 允许的最大摄像头变焦倍数，实际应用取本值与设备允许倍数的最小值

@interface PLVRTCStreamerManager ()<PLVBLinkMicManagerDelegate, PLVBeautyManagerDelegate, PLVBeautyResourceManagerDelegate>

#pragma mark 状态
@property (nonatomic, assign) BOOL pushStreamStarted;
@property (nonatomic, assign) BOOL channelLinkMicOpen;
@property (nonatomic, assign) PLVChannelLinkMicMediaType channelLinkMicMediaType;
@property (nonatomic, assign) BOOL originalLocalCameraOpen; /// 退后台前的‘localCameraOpen’值
@property (nonatomic, assign) PLVRTCStreamerMixLayoutType mixLayoutType;
@property (nonatomic, assign) PLVBLinkMicStreamScale streamScale;
@property (nonatomic, assign) PLVBLinkMicStreamQuality streamQuality;
@property (nonatomic, assign) PLVBRTCVideoQosPreference videoQosPreference;
@property (nonatomic, assign) PLVBRTCStreamSourceType streamSourceType;
@property (nonatomic, copy) NSString *streamQualityLevel;
@property (nonatomic, assign) PLVBRTCVideoMirrorMode localVideoStreamMirrorMode;
@property (nonatomic, assign) BOOL waitingNextActionUpdate;

#pragma mark 数据
@property (nonatomic, copy) NSString * channelId;
@property (nonatomic, copy) NSString * userRTCId;
@property (nonatomic, copy) NSString * rtmpUrl;
@property (nonatomic, copy) NSString * stream;
@property (nonatomic, copy) NSString * sessionId;
@property (nonatomic, strong) PLVLinkMicGetTokenModel * getTokenModel;
@property (nonatomic, strong) NSArray <PLVRTCStreamerMixUser *> * mixUserList;
@property (nonatomic, strong) PLVClientPushStreamTemplateVideoParams *currentVideoParams;
@property (nonatomic, assign) NSTimeInterval updateActionDate;
@property (nonatomic, assign) BOOL isContinue;
@property (nonatomic, assign) CGFloat currentCameraZoomRatio;   //!< 当前摄像头的变焦倍数，默认值为1
@property (nonatomic, assign) PLVRTCStreamerMixLayoutType lastMixLayoutType;
@property (nonatomic, assign) BOOL shouldApplyLinkMicNewStrategy;
@property (nonatomic, assign) PLVChannelLinkMicMediaType defaultChannelLinkMicMediaType;
@property (nonatomic, assign) BOOL hadSetupLinkMicStrategy;

/// 推流时间计算
@property (nonatomic, assign) NSTimeInterval startPushStreamTimestamp;

/// 退至后台时间计算
@property (nonatomic, assign) NSTimeInterval enterBackgroundTimestamp;/// 单位：秒；退至后台时间戳；
@property (nonatomic, assign) NSTimeInterval enterBackgroundDuration; /// 单位：秒；单次推流中的‘退至后台’已结算时长；(回到前台后结算)
@property (nonatomic, assign, readonly) NSTimeInterval enterBackgroundTotalDuration; /// 单位：秒；单次推流中的‘退至后台’总时长；(包括当前此刻，无论是否已回至前台)；

/// 重连时间计算
@property (nonatomic, assign) NSTimeInterval reconnectingTimestamp; /// 单位：秒；单次开始重连时间戳
@property (nonatomic, assign) NSTimeInterval reconnectingDuration; ///  单位：秒；重连的已结算时长 (包含每一次重连的总时长，重连结束后结算)
@property (nonatomic, copy) NSString *lastMixAction; /// 记录最后一次推流操作的Action
@property (nonatomic, assign) BOOL mixActionCompleted; /// 记录推流操作请求完成

#pragma mark 对象
@property (nonatomic, strong) PLVBLinkMicManager * rtcManager;
@property (nonatomic, strong) PLVBeautyManager * beautyManager;
@property (nonatomic, strong) PLVBeautyResourceManager * beautyResourceManager;
@property (nonatomic, strong) NSTimer * pushStreamTimer;
@property (nonatomic, strong) NSTimer * SEITimer;
@property (nonatomic, strong) NSTimer * reconnectingTimer;
@property (nonatomic, strong) NSTimer * mixActionRetryTimer;
@property (nonatomic, copy) void (^joinChannelSuccessBlock) (void);
@property (nonatomic, strong) AVCaptureDevice *currentCamera;

@end

@implementation PLVRTCStreamerManager


#pragma mark - [ Life Cycle ]
- (void)dealloc{
    [self closeBeautyProcess];
        
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    self.delegate = nil;
    
    /// 定时器处理
    [self stopSEITimer];
    [self stopPushStreamTimer];
    [self stopReconnectingTimer];
    [self stopMixActionRetryTimer];
    
    /// RTC处理
    [self destroyRTCEngine];
    
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer,@"%s", __FUNCTION__);
}

- (instancetype)init{
    if (self = [super init]) {
        [self initData];
        [self resetData];
        [self setup];
    }
    return self;
}


#pragma mark - [ Public Methods ]
#pragma mark 基础调用
/// 创建 RTC推流管理器
+ (instancetype)rtcStreamerManagerWithRTCType:(NSString *)rtcType channelId:(NSString *)channelId {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (channelId:%@)",__FUNCTION__, channelId); /// 一般情况下不允许打印 rtcType

    if (![PLVFdUtil checkStringUseable:rtcType] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【params invalid】(rtcType:%@, channelId:%@)",__FUNCTION__,rtcType,channelId);
        return nil;
    }
    
    PLVRTCStreamerManager * rtcStreamerManager;
    
    /// 创建 核心RTC管理器
    PLVBLinkMicManager * rtcManager = [PLVBLinkMicManager linkMicManagerWithRTCType:rtcType];
    if (rtcManager) {
        /// 创建 RTC推流管理器
        rtcStreamerManager = [[PLVRTCStreamerManager alloc]init];
        rtcStreamerManager.rtcManager = rtcManager;
        rtcStreamerManager.channelId = channelId;
        PLV_KEY_INFO(@"mediaIdList", channelId);
        /// 配置 核心RTC管理器
        rtcManager.delegate = rtcStreamerManager;
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s rtc streamer manager init failed with (rtcType:%@)", __FUNCTION__, rtcType);
    }
    return rtcStreamerManager;
}

- (void)setupAppGroup:(NSString *)appGroup rtcType:(NSString *)rtcType{
    if (![PLVFdUtil checkStringUseable:appGroup] ||
        ![PLVFdUtil checkStringUseable:rtcType]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【params invalid】(appGroup:%@, rtcType:%@)",__FUNCTION__,appGroup,rtcType);
        return;
    }
    
    [self.rtcManager setupAppGroup:appGroup rtcType:rtcType];
}

- (void)setLinkMicNewStrategyEnabled:(BOOL)linkMicNewStrategyEnabled interactNumLimit:(NSUInteger)interactNumLimit defaultChannelLinkMicMediaType:(PLVChannelLinkMicMediaType)defaultChannelLinkMicMediaType {
    if (!self.hadSetupLinkMicStrategy) {
        self.shouldApplyLinkMicNewStrategy = linkMicNewStrategyEnabled && (interactNumLimit > 0);
        self.defaultChannelLinkMicMediaType = defaultChannelLinkMicMediaType;
        if (self.shouldApplyLinkMicNewStrategy) {
            self.channelLinkMicMediaType = defaultChannelLinkMicMediaType;
        }
        self.hadSetupLinkMicStrategy = YES;
    }
}

/// 更新 RTC Token
- (void)updateRTCTokenWith:(PLVLinkMicGetTokenModel *)model completion:(nullable void (^)(BOOL updateResult))completion{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (model:%@ completion:%@)",__FUNCTION__, model, completion);

    PLVBLinkMicRTCType rtcType = self.rtcManager.rtcType;
    NSString * rtcTypeName = @"";
    if (rtcType == PLVBLinkMicRTCType_AG) {
        rtcTypeName = @"ARTC";
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        rtcTypeName = @"URTC";
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        rtcTypeName = @"ZRTC";
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        rtcTypeName = @"TRTC";
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        rtcTypeName = @"VOLC";
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeLinkMic, @"%s update failed with 【param illegal】 (rtcType:%@)", __FUNCTION__, rtcType);
    }
    
    __weak typeof(self) weakSelf = self;
    void(^getRTCTokenSuccessBlock)(NSDictionary * _Nonnull dict) = ^(NSDictionary * _Nonnull dict){
        NSString *data = dict[@"data"];
        if (![PLVFdUtil checkStringUseable:data]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s get token failed with 【param illegal】(data:%@)", __FUNCTION__, data);
            if (completion) { completion(NO); }
        }else{
            int result = [weakSelf.rtcManager updateLinkMicTokenWithStr:data];
            if (completion) { completion(result == 0 ? YES : NO); }
        }
    };
    
    void(^getRTCTokenFailBlock)(NSError * _Nonnull error) = ^(NSError * _Nonnull error){
        if (completion) { completion(NO); }
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s get token failed with 【%@】(rtcTypeName:%@)", __FUNCTION__, error, rtcTypeName);
        NSError * finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_UpdateRTCTokenFailedAuthError errorDescription:nil];
        finalError = PLVErrorWithUnderlyingError(finalError, error);
        [weakSelf callbackForDidOccurError:finalError];
    };

    self.getTokenModel = model;
    if (rtcType == PLVBLinkMicRTCType_AG) {
        [PLVLivePrivateAPI getAgRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        [PLVLivePrivateAPI getUcRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        [PLVLivePrivateAPI getZeRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        [PLVLivePrivateAPI getTRTCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        [PLVLivePrivateAPI getVOLCTokenWithModel:model success:getRTCTokenSuccessBlock failure:getRTCTokenFailBlock];
    }
}

/// 配置 本地预览画面模型
- (void)setupLocalPreviewWithCanvasModel:(PLVBRTCVideoViewCanvasModel *)canvasModel{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (canvasModel:%@, checkModelValid:%d)",__FUNCTION__, canvasModel, canvasModel.checkModelValid);

    [self prepareRTCEngine];
    [self.rtcManager setupLocalPreviewWithCanvasModel:canvasModel];
}

/// 开始 本地麦克风、摄像头画面预览
- (void)startLocalMicCameraPreviewByDefault{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s",__FUNCTION__);
    /// 设置硬件默认配置
    if (self.currentLocalPreviewCanvasModel) {
        [self openLocalUserMic:self.micDefaultOpen];
        [self openLocalUserCamera:self.cameraDefaultOpen completion:nil];
        [self switchLocalUserCamera:self.cameraDefaultFront];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【currentLocalPreviewCanvasModel is 'nil'",__FUNCTION__);
    }
}

/// 加入 RTC频道
- (int)joinRTCChannelWithUserRTCId:(NSString *)userRTCId{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (userRTCId:%@)",__FUNCTION__, userRTCId);

    if (!self.rtcTokenAvailable) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【rtcTokenAvailable is 'NO'】(should call [updateRTCTokenWith:completion:] before)",__FUNCTION__);
        return -1;
    }
    
    if (![PLVFdUtil checkStringUseable:self.channelId] ||
        ![PLVFdUtil checkStringUseable:userRTCId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【params invalid】(channelId:%@, userRTCId:%@)",__FUNCTION__,self.channelId,userRTCId);
        return -2;
    }else{
        self.userRTCId = userRTCId;
    }
    
    [self prepareRTCEngine];

    if (!self.hadJoinedRTC) {
        [self.rtcManager joinRtcChannelWithChannelId:self.channelId userLinkMicId:userRTCId];
    }
    return 0;
}

/// 退出 RTC频道
- (int)leaveRTCChannel{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s",__FUNCTION__);

    return [self.rtcManager leaveRtcChannel];
}

- (void)switchRoleTypeTo:(PLVBLinkMicRoleType)roleType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (roleType:%d)", __FUNCTION__, roleType);

    [self.rtcManager setUserRoleTo:roleType];
    if (roleType == PLVBLinkMicRoleBroadcaster) {
        /// 当角色改变时，同步刷新mute状态
        /// 需延时1秒，兼容URTC下，publish期间不允许 mute 问题
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (!self.rtcManager.localCameraOpen) {
                [self.rtcManager muteLocalVideoStream:YES];
            }
            if (!self.rtcManager.localMicOpen) {
                [self.rtcManager muteLocalAudioStream:YES];
            }
        });
    }
}

#pragma mark CDN流管理
/// 开始推流
- (void)startPushStreamWithStream:(NSString *)stream rtmpUrl:(NSString *)rtmpUrl continueLastLive:(BOOL)isContinue{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (stream:%@, rtmpurl:%@)",__FUNCTION__, stream, rtmpUrl);
    
    if (!self.rtcTokenAvailable) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream failed with【rtcTokenAvailable is 'NO'】(should call [updateRTCTokenWith:completion:] before)");
        return;
    }
    
    if (self.pushStreamStarted) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream failed, had started");
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:stream]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream failed with 【params invalid】(stream:%@)",stream);
        return;
    }

    if (![PLVFdUtil checkStringUseable:rtmpUrl]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream failed with 【params invalid】(rtmpUrl:%@)",rtmpUrl);
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:self.channelId] ||
        ![PLVFdUtil checkStringUseable:self.userRTCId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream failed with 【params invalid】(channelId:%@, userRTCId:%@)",__FUNCTION__,self.channelId,self.userRTCId);
        return;
    }
    
    self.stream = stream;
    self.rtmpUrl = rtmpUrl;
    self.isContinue = isContinue;
    
    [self prepareRTCEngine];
    
    if (!self.hadJoinedRTC) {
        /// 设置 加入RTC房间成功 事件Block
        __weak typeof(self) weakSelf = self;
        self.joinChannelSuccessBlock = ^{
            [weakSelf requestForSetupStream];
        };
        
        /// 加入RTC房间
        [self joinRTCChannelWithUserRTCId:self.userRTCId];
    }else{
        /// 请求 配置流
        [self requestForSetupStream];
    }
}

/// 停止推流
- (void)stopPushStream{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s", __FUNCTION__);

    /// 停止推流
    [self stopPushStreamReal];
    
    /// 切换 ‘是否推流已开始’ 状态
    [self changePushStreamState:NO];
    
    /// 清理数据
    self.rtmpUrl = nil;
}

- (void)subscribeStreamWithRTCUserId:(NSString *)rtcUserId renderOnView:(UIView *)renderSuperView mediaType:(PLVBRTCSubscribeStreamMediaType)mediaType{
    [self.rtcManager subscribeStreamWithRTCUserId:rtcUserId renderOnView:renderSuperView mediaType:mediaType];
}

- (void)unsubscribeStreamWithRTCUserId:(NSString *)rtcUserId{
    [self.rtcManager unsubscribeStreamWithRTCUserId:rtcUserId];
}

- (void)setStickerImage:(UIImage *)stickerImage{
    [self.rtcManager setStickerImage:stickerImage];
}

- (void)setAIMattingMode:(PLVBLinkMicAIMattingMode)mode image:(UIImage *)matBgImage{
    [self.rtcManager setAIMattingMode:mode image:matBgImage];
}

- (void)setupStreamScale:(PLVBLinkMicStreamScale)streamScale{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (streamScale:%d)", __FUNCTION__, streamScale);
    _streamScale = streamScale;
    
    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
    
    if (self.pushStreamStarted) {
        /// 更新混流配置
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update];
    }
}

- (void)setupStreamQuality:(PLVBLinkMicStreamQuality)streamQuality{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (streamQuality:%d)", __FUNCTION__, streamQuality);
    NSString *info = [NSString stringWithFormat:@"setBitrate: from %@ to %@", @(_streamQuality), @(streamQuality)];
    if ([PLVLiveVideoConfig sharedInstance].clientPushStreamTemplateEnabled) {
        NSString *streamQualityLevel = nil;
        int i = (int)streamQuality/4.0;
        NSArray <PLVClientPushStreamTemplateVideoParams *> *videoParams = [PLVLiveVideoConfig sharedInstance].videoParams;
        if ([PLVFdUtil checkArrayUseable:videoParams] && i < videoParams.count && i >= 0) {
            streamQualityLevel =videoParams[i].qualityLevel;
        }
        _streamQualityLevel = streamQualityLevel;
        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (streamQualityLevel:%@)", __FUNCTION__, streamQualityLevel);
        info = [NSString stringWithFormat:@"setBitrate: from %@ to %@", _streamQualityLevel, streamQualityLevel];
    }
    _streamQuality = streamQuality;

    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"setBitrate" modul:PLVWELogModulLink information:info patch:YES];

    if (self.pushStreamStarted) {
        /// 更新混流配置
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update];
    }
}

- (void)setupStreamQualityLevel:(NSString *)streamQualityLevel {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (streamQualityLevel:%@)", __FUNCTION__, streamQualityLevel);
    NSString *info = [NSString stringWithFormat:@"setupStreamQualityLevel: from %@ to %@", _streamQualityLevel, streamQualityLevel];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"setupStreamQualityLevel" modul:PLVWELogModulLink information:info patch:YES];
    
    if (![PLVFdUtil checkStringUseable:streamQualityLevel]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s with (streamQualityLevel:%@)", __FUNCTION__, streamQualityLevel);
        return;
    }
    
    _streamQualityLevel = streamQualityLevel;
    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];

    if (self.pushStreamStarted) {
        /// 更新混流配置
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update];
    }
}

- (void)setupLocalVideoStreamMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (mirrorMode:%d)", __FUNCTION__, mirrorMode);
    _localVideoStreamMirrorMode = mirrorMode;
    
    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
}

- (void)setupVideoQosPreference:(PLVBRTCVideoQosPreference)qosPreference {
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (qosPreference:%d)", __FUNCTION__, qosPreference);
    _videoQosPreference = qosPreference;
    
    [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
}

- (void)setupMixLayoutType:(PLVRTCStreamerMixLayoutType)mixLayoutType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with (mixLayoutType:%d)", __FUNCTION__, mixLayoutType);
    _lastMixLayoutType = _mixLayoutType;
    _mixLayoutType = mixLayoutType;
    
    __weak typeof(self)weakSelf = self;
    if (self.pushStreamStarted) {
        /// 更新混流配置
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update callback:^(BOOL failed) {
            if (failed) {
                weakSelf.mixLayoutType = weakSelf.lastMixLayoutType;
                [weakSelf callbackForUpdateMixLayoutDidOccurError:weakSelf.lastMixLayoutType];
            }
        }];
    }
}

- (void)setupMixUserList:(NSArray<PLVRTCStreamerMixUser *> *)mixUserList{
    if (![PLVFdUtil checkArrayUseable:mixUserList]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"setup mix user list failed with 【mixUserList invalid】(mixUserList:%@)",mixUserList);
        return;
    }
    self.mixUserList = mixUserList;
    
    if (self.pushStreamStarted) {
        /// 更新混流配置
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update];
    }
}

- (void)switchLocalUserStreamSourceType:(PLVBRTCStreamSourceType)streamSourceType{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"%s with streamSourceType : %ld",__FUNCTION__, streamSourceType);
    _streamSourceType = streamSourceType;
    [self.rtcManager switchPublishStreamSourceType:streamSourceType];
    if ([PLVLiveVideoConfig sharedInstance].clientPushStreamTemplateEnabled) {
        [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
        
        if (self.pushStreamStarted) {
            /// 更新混流配置
            [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update];
        }
    }
}

#pragma mark 本地硬件管理
- (void)setupLocalVideoPreviewMirrorMode:(PLVBRTCVideoMirrorMode)mirrorMode{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"set local video preview mirror mode: %@", mirrorMode ? @"YES" : @"NO");
    [self.rtcManager setupLocalVideoPreviewMirrorMode:mirrorMode];
}

- (void)switchLocalUserCamera:(BOOL)frontCamera{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"switch local user camera: %@", frontCamera ? @"YES" : @"NO");
    [self.rtcManager switchLocalUserCamera:frontCamera];
    [self searchCurrentCamera];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"switchCamera" modul:PLVWELogModulLink information:nil patch:YES];
}

- (void)openLocalUserCameraTorch:(BOOL)openCameraTorch{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"open local user camera torch: %@", openCameraTorch ? @"YES" : @"NO");
    [self.rtcManager openLocalUserCameraTorch:openCameraTorch];
}

- (void)openLocalUserCamera:(BOOL)openCamera completion:(nullable void (^)(int openResult))completion{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"open local user camera : %@", openCamera ? @"YES" : @"NO");
    int changePlaceholderResultCode = [self.rtcManager localVideoStreamOpenPlaceholder:!openCamera];
    if (changePlaceholderResultCode == 0) {
        BOOL immediateExecute = (self.rtcManager.streamPublishing ? (openCamera?NO:YES) : YES);
        __weak typeof(self) weakSelf = self;
        void(^openLocalUserCameraBlock)(void) = ^{
            NSString *event = openCamera ? @"enableLocalVideo" : @"disableLocalVideo";
            [[PLVWLogReporterManager sharedManager] reportWithEvent:event modul:PLVWELogModulLink information:nil patch:YES];
            int openLocalUserCameraResultCode = [weakSelf.rtcManager openLocalUserCamera:openCamera];
            [weakSelf searchCurrentCamera];
            if (completion) { completion(openLocalUserCameraResultCode); };
        };
        immediateExecute ? openLocalUserCameraBlock() : dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), openLocalUserCameraBlock);
    }else{
        if (completion) { completion(changePlaceholderResultCode); };
    }
}

- (void)openLocalUserMic:(BOOL)openMic{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"open local user mic : %@", openMic ? @"YES" : @"NO");
    NSString *event = openMic ? @"enableLocalAudio" : @"disableLocalAudio";
    [[PLVWLogReporterManager sharedManager] reportWithEvent:event modul:PLVWELogModulLink information:nil patch:YES];
    [self.rtcManager openLocalUserMic:openMic];
}

- (void)setCameraZoomRatio:(CGFloat)zoomRatio {
    if (self.currentCamera) {
        if ([self.currentCamera lockForConfiguration:nil]) {
            CGFloat maxZoomRatio = [self.currentCamera.activeFormat videoMaxZoomFactor];
            maxZoomRatio = MIN(maxZoomRatio, MaxCameraZoomRatio);
            if (zoomRatio < 1) {
                zoomRatio = 1;
            }else if (zoomRatio > maxZoomRatio) {
                zoomRatio = maxZoomRatio;
            }
            self.currentCamera.videoZoomFactor = zoomRatio;
            [self.currentCamera unlockForConfiguration];
            self.currentCameraZoomRatio = zoomRatio;
        }
    }
}

- (CGFloat)getCameraZoomRatio {
    return self.currentCameraZoomRatio;
}

- (CGFloat)getMaxCameraZoomRatio {
    if (!self.currentCamera) {
        return 1;
    }
    if ([self.currentCamera lockForConfiguration:nil]) {
        CGFloat maxZoomRatio = [self.currentCamera.activeFormat videoMaxZoomFactor];
        [self.currentCamera unlockForConfiguration];
        return MIN(maxZoomRatio, MaxCameraZoomRatio);
    }
    return 1;
}

#pragma mark 连麦事件管理
- (void)openVideoLinkMic:(BOOL)open emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"open video linkMic with (open:%zd)", open);
    [self emitSocketMessage_OPEN_MICROPHONE_open:open videoType:YES emitCompleteBlock:emitCompleteBlock];
}

- (void)openAudioLinkMic:(BOOL)open emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"open audio linkMic with (open:%zd)", open);
    [self emitSocketMessage_OPEN_MICROPHONE_open:open videoType:NO emitCompleteBlock:emitCompleteBlock];
}

- (void)closeLinkMicEmitCompleteBlock:(void (^)(BOOL))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"close linkMic");
    self.channelLinkMicOpen = NO;
    if (self.channelLinkMicMediaType == PLVChannelLinkMicMediaType_Audio) {
        [self openAudioLinkMic:NO emitCompleteBlock:emitCompleteBlock];
    }else if(self.channelLinkMicMediaType == PLVChannelLinkMicMediaType_Video) {
        [self openVideoLinkMic:NO emitCompleteBlock:emitCompleteBlock];
    }else{
        [self openVideoLinkMic:NO emitCompleteBlock:emitCompleteBlock];
    }
    if (!self.shouldApplyLinkMicNewStrategy) {
        self.channelLinkMicMediaType = PLVChannelLinkMicMediaType_Unknown;
    }
}

- (void)allowRemoteUserJoinLinkMic:(NSDictionary *)userDict emitCompleteBlock:(nullable void (^)(BOOL success))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"allow user join linkMic with (userDict:%@)", userDict);
    [self allowRemoteUserJoinLinkMic:userDict raiseHand:nil emitCompleteBlock:emitCompleteBlock];
}

- (void)allowRemoteUserJoinLinkMic:(NSDictionary *)userDict raiseHand:(NSString *)raiseHand emitCompleteBlock:(void (^)(BOOL))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"allow user join linkMic with (userDict:%@ raiseHand:%zd)", userDict, raiseHand);
    NSString * rtcUserId = [NSString stringWithFormat:@"%@",userDict[@"userId"]];
    if ([rtcUserId isEqualToString:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"allowUserJoinLinkMic failed, 【rtcUserId is self %@】", rtcUserId);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
        return;
    }
    [self emitSocketMessage_joinResponse_userDict:userDict raiseHand:raiseHand emitCompleteBlock:emitCompleteBlock];
}

- (void)muteRemoteUserMic:(NSDictionary *)userDict muteMic:(BOOL)muteMic emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"mute remote user mic with (userDict:%@, micMute:%zd)", userDict, muteMic);
    NSString * rtcUserId = [NSString stringWithFormat:@"%@",userDict[@"userId"]];
    if ([rtcUserId isEqualToString:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s mute remote user mic failed, 【rtcUserId is self %@】", __FUNCTION__, rtcUserId);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
        return;
    }
    [self emitSocketMessage_MUTE_USER_MEDIA_userDict:userDict videoType:NO mute:muteMic emitCompleteBlock:emitCompleteBlock];
}

- (void)muteRemoteUserCamera:(NSDictionary *)userDict muteCamera:(BOOL)muteCamera emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"mute remote user camera with (userDict:%@, muteCamera:%zd)", userDict, muteCamera);
    NSString * rtcUserId = [NSString stringWithFormat:@"%@",userDict[@"userId"]];
    if ([rtcUserId isEqualToString:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s mute remote user camera failed, 【rtcUserId is self %@】", __FUNCTION__, rtcUserId);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
        return;
    }
    [self emitSocketMessage_MUTE_USER_MEDIA_userDict:userDict videoType:YES mute:muteCamera emitCompleteBlock:emitCompleteBlock];
}

- (void)closeRemoteUserLinkMic:(NSDictionary *)userDict emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"close remote user linkMic with (userDict:%@)", userDict);
    NSString * rtcUserId = [NSString stringWithFormat:@"%@",userDict[@"userId"]];
    if ([rtcUserId isEqualToString:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s close remote user linkMic failed, 【rtcUserId is self %@】", __FUNCTION__, rtcUserId);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
        return;
    }
    [self emitSocketMessage_OPEN_MICROPHONE_closeRemoteUser_userDict:userDict emitCompleteBlock:emitCompleteBlock];
}

- (void)forceCloseRemoteUserLinkMic:(NSDictionary *)userDict emitCompleteBlock:(nullable void (^)(BOOL emitSuccess))emitCompleteBlock{
    PLV_LOG_DEBUG(PLVConsoleLogModuleTypeStreamer, @"force close remote user linkMic with (userDict:%@)", userDict);
    [self forceCloseRemoteUserLinkMic:userDict retryCount:0 emitCompleteBlock:emitCompleteBlock];
}

- (BOOL)removeAllAudiences {
    BOOL success = [self emitSocketMessage_OPEN_MICROPHONE_removeAllWithCallback:nil];
    return success;
}

- (void)forceCloseRemoteUserLinkMic:(NSDictionary *)userDict retryCount:(NSInteger)retryCount emitCompleteBlock:(void (^)(BOOL))emitCompleteBlock {
    __block NSInteger retryNum = retryCount;
    NSString *userId = PLV_SafeStringForDictKey(userDict, @"loginId");
    NSString *linkMicId = PLV_SafeStringForDictKey(userDict, @"userId");
    if ([PLVFdUtil checkStringUseable:linkMicId] && ![PLVFdUtil checkStringUseable:userId]) {
        userId = linkMicId;
    } else if (![PLVFdUtil checkStringUseable:linkMicId] && [PLVFdUtil checkStringUseable:userId]) {
        linkMicId = userId;
    }
    [PLVLiveVideoAPI requestLinkMicRemoveUser:self.channelId userId:userId linkMicId:linkMicId completion:^(BOOL success) {
        if (!success && retryCount < 3) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                retryNum ++;
                [self forceCloseRemoteUserLinkMic:userDict retryCount:retryNum emitCompleteBlock:emitCompleteBlock];
            });
        } else {
            if (emitCompleteBlock) {
                emitCompleteBlock(success);
            }
        }
    } failure:^(NSError * _Nonnull error) {
        if (retryCount < 3) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                retryNum ++;
            });
        } else {
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
        }
    }];
}

#pragma mark 美颜管理

/// 初始化美颜功能
- (void)initBeauty {
    self.beautyResourceManager = [NSClassFromString(@"PLVBeautyResourceManager") sharedManager];
    self.beautyResourceManager.delegate = self;
    // 从服务器更新美颜信息，更新结果通过代理回调
    [self.beautyResourceManager updateResourceInfoFromServer];
}

/// 保利威轻美颜初始化
- (void)initLightBeauty{
    [self setupLightBeauty];
}

/// 开启或关闭 美颜处理
/// 开启后，本地采集的视频数据将会通过代理回调到本类进行美颜处理
/// @param enabled 开启或停止 (YES:开启；NO:关闭)
- (void)enableBeautyProcess:(BOOL)enabled {
    if (!self.beautyManager) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【beauty is not initialized】",__FUNCTION__);
    }
    if (self.rtcManager.engineIsReady) {
        [self.rtcManager enableLocalVideoFrameProcess:enabled];
    }else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【engineIsReady is 'NO'】",__FUNCTION__);
    }
    
    BOOL enableBeauty = (self.beautyManager && self.rtcManager.engineIsReady && enabled);
    NSString *info = [NSString stringWithFormat:@"enableBeauty: %@", enableBeauty ? @"YES" : @"NO"];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"enableBeauty" modul:PLVWELogModulBeauty information:info patch:YES];
}

// 关闭美颜处理, 在dealloc 函数中调用
- (void)closeBeautyProcess{
    if (self.beautyManager) {
        // 是否手动释放 美颜引擎
    }
    if (self.rtcManager.engineIsReady) {
        // 关闭美颜开关
        self.rtcManager.beautyLightEngine = nil;
        [self.rtcManager enableLocalVideoFrameProcess:NO];
    }else {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"%s failed with 【engineIsReady is 'NO'】",__FUNCTION__);
    }
}

/// 获取美颜管理器
- (PLVBeautyManager *)shareBeautyManager {
    return self.beautyManager;
}

#pragma mark 降噪管理

- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level {
    return [self.rtcManager switchNoiseCancellationLevelTo:level];
}

#pragma mark 外接设备管理

- (int)enableExternalDevice:(BOOL)enabled {
    return [self.rtcManager enableExternalDevice:enabled];
}

#pragma mark Getter
- (BOOL)hadJoinedRTC{
    return self.rtcManager.hadJoinedRTC;
}

- (PLVBRTCVideoMirrorMode)localVideoMirrorMode{
    return self.rtcManager.localVideoMirrorMode;
}

- (BOOL)localCameraFront{
    return self.rtcManager.localCameraFront;
}

- (BOOL)localCameraTorchOpen{
    return self.rtcManager.localCameraTorchOpen;
}

- (BOOL)localCameraOpen{
    return self.rtcManager.localCameraOpen;
}

- (BOOL)localVideoStreamMute{
    return self.rtcManager.localVideoStreamMute;
}

- (CGFloat)localMicVolume{
    return self.rtcManager.localMicVolume;
}

- (BOOL)localMicOpen{
    return self.rtcManager.localMicOpen;
}

- (BOOL)localAudioStreamMute{
    return self.rtcManager.localAudioStreamMute;
}

- (PLVBLinkMicNoiseCancellationLevel)localNoiseCancellationLevel{
    return self.rtcManager.localNoiseCancellationLevel;
}

- (BOOL)localExternalDeviceEnabled {
    return self.rtcManager.localExternalDeviceEnabled;
}

- (PLVBLinkMicConnectionStateType)connectionState{
    return self.rtcManager.connectionState;
}

- (PLVBLinkMicNetworkQuality)networkQuality{
    return self.rtcManager.networkQuality;
}

- (PLVBRTCNetworkQuality)localNetworkQuality{
    return self.rtcManager.localNetworkQuality;
}

- (BOOL)rtcTokenAvailable{
    return self.rtcManager.rtcTokenAvailable;
}

- (PLVBRTCStreamSourceType)publishStreamSourceType{
    return self.rtcManager.publishStreamSourceType;
}

/// 推流时间计算
- (NSTimeInterval)pushStreamValidDuration{
    NSTimeInterval valid_LiveDuration = self.pushStreamTotalDuration - self.reconnectingTotalDuration;
    if (valid_LiveDuration <= 0) { valid_LiveDuration = 0; }
    return valid_LiveDuration;
}

- (NSTimeInterval)pushStreamTotalDuration{
    NSTimeInterval total_pushStreamDuration = 0;
    if (_startPushStreamTimestamp > 0) {
        total_pushStreamDuration = [self getCurrentTimestampSec] - _startPushStreamTimestamp;
    }
    return total_pushStreamDuration;
}

/// 退至后台时间计算
- (NSTimeInterval)enterBackgroundTotalDuration{
    NSTimeInterval thisTime_enterBackgroundDuration = 0;
    if (self.enterBackgroundTimestamp > 0) {
        thisTime_enterBackgroundDuration = [self getCurrentTimestampSec] - self.enterBackgroundTimestamp;
    }
    NSTimeInterval total_enterBackgroundDuration = _enterBackgroundDuration + thisTime_enterBackgroundDuration;
    if (total_enterBackgroundDuration <= 0) { total_enterBackgroundDuration = 0; }
    return total_enterBackgroundDuration;
}

/// 重连时间计算
- (NSTimeInterval)reconnectingThisTimeDuration{
    NSTimeInterval thisTime_reconnectingDuration = 0;
    if (self.reconnectingTimestamp > 0) {
        thisTime_reconnectingDuration = [self getCurrentTimestampSec] - self.reconnectingTimestamp;
    }
    return thisTime_reconnectingDuration;
}

- (NSTimeInterval)reconnectingTotalDuration{
    NSTimeInterval total_reconnectingTotalDuration = _reconnectingDuration + self.reconnectingThisTimeDuration;
    if (total_reconnectingTotalDuration <= 0) { total_reconnectingTotalDuration = 0; }
    return total_reconnectingTotalDuration;
}

- (PLVBRTCVideoViewCanvasModel *)currentLocalPreviewCanvasModel{
    return self.rtcManager.currentLocalPreviewCanvasModel;
}


#pragma mark - [ Private Methods ]
- (void)initData{
    _streamScale = PLVBLinkMicStreamScale16_9;
    _streamQuality = PLVBLinkMicStreamQuality180P;
    _localVideoStreamMirrorMode = PLVBRTCVideoMirrorMode_Disabled;
    self.currentCameraZoomRatio = 1;
}

- (void)resetData{
    /// 初始化 数据
    self.startPushStreamTimestamp = 0;
    
    self.enterBackgroundTimestamp = 0;
    self.enterBackgroundDuration = 0;
    
    self.reconnectingTimestamp = 0;
    self.reconnectingDuration = 0;
}

- (void)setup{
    /// 设置 监听 事件
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didBecomeActive:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
}

- (void)setupBeauty {
    self.beautyManager = [NSClassFromString(@"PLVBeautyManager") beautyManagerWithBeautyType:@"BytedEffect"];
    self.beautyManager.delegate = self;
    // 若rtc已经初始化，则美颜需要跟随rtc在同一个opengl环境
    if (self.rtcManager.engineIsReady) {
        self.beautyManager.eaglContext = self.rtcManager.eaglContext;
    }
    int result = [self.beautyManager setupBeautyWithResource:self.beautyResourceManager];
    [self callbackForBeautyDidInitWithResult:result];
}

- (void)setupLightBeauty{
    // SDK 权限验证
    if (![PLVFdUtil checkStringUseable:[PLVLiveVideoConfig sharedInstance].beautyType] ||
        ![[PLVLiveVideoConfig sharedInstance].beautyType isEqualToString:@"gpu"]) {
         int result = -1;
        [self callbackForBeautyDidInitWithResult:result];
        return;
    }

    self.beautyManager = [NSClassFromString(@"PLVBeautyManager") beautyManagerWithBeautyType:@"GPUPixel"];
    self.beautyManager.delegate = self;
    // 若rtc已经初始化，则美颜需要跟随rtc在同一个opengl环境
    // 轻美颜无需用到opengl
//    if (self.rtcManager.engineIsReady) {
//        self.beautyManager.eaglContext = self.rtcManager.eaglContext;
//        
//    }
    
    /// 如果是轻引擎  放在RTC 引擎内部进行数据处理
    /// 因为美颜引擎的限制  RTC内部数据不能回调到StreamerManager 进行处理
    self.rtcManager.beautyLightEngine = self.beautyManager.beautyEngine;

    // 美颜sdk 初始化完成回调
    int result = 0;
    [self callbackForBeautyDidInitWithResult:result];
}

- (BOOL)changeSessionId:(NSString *)sessionId{
    BOOL canUpdate = ((sessionId == nil) || ([sessionId isKindOfClass:NSString.class] && sessionId.length > 0));
    if (!canUpdate) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"update sessionId failed with 【parma illegal】 (sessionId:%@)", sessionId);
        return canUpdate;
    }
    
    BOOL needCallback = NO;
    if (_sessionId) {
        needCallback = ![_sessionId isEqualToString:sessionId];
    }else{
        needCallback = (sessionId != nil);
    }
    _sessionId = sessionId;
    
    if (needCallback) {
        [self callbackForSessionIdDidChanged];
    }
    return canUpdate;
}

- (NSError *)errorWithCode:(NSInteger)code errorDescription:(NSString *)errorDes{
    return PLVErrorCreate(@"net.plv.PLVRTCStreamerManager", code, errorDes);
}

/// 寻找当前的摄像头
- (void)searchCurrentCamera {
    self.currentCameraZoomRatio = 1;
    NSArray *devices = [AVCaptureDevice devicesWithMediaType:AVMediaTypeVideo];
    for (AVCaptureDevice *device in devices)
    {
        if (self.localCameraFront &&
            [device position] == AVCaptureDevicePositionFront)
        {
            self.currentCamera = device;
            self.currentCameraZoomRatio = self.currentCamera.videoZoomFactor;
            break;
        }else if (!self.localCameraFront &&
                  [device position] == AVCaptureDevicePositionBack) {
            self.currentCamera = device;
            self.currentCameraZoomRatio = self.currentCamera.videoZoomFactor;
            break;
        }
    }
}

#pragma mark Streamer
/// 准备 RTC引擎
- (void)prepareRTCEngine{
    if (!self.rtcManager.engineIsReady) {
        [self.rtcManager createRTCEngine];
        [self.rtcManager setupVideoEncoderConfiguration:[self updateVideoEncoderConfiguration]];
    }
}

- (void)destroyRTCEngine{
    if (self.rtcManager.engineIsReady) {
        [self.rtcManager destroyRTCEngine];
    }
}

/// 开始推流 (真正开始推流，非业务逻辑上的)
- (BOOL)startPushStreamReal{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer, @"start push stream real");
    
    if (self.pushStreamStarted) {
        /// 已开始推流
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"start push stream real failed, had started");
        return NO;
    }else{
        /// 未开始推流
        [self resetData];
        
        [self.rtcManager setupLiveTranscoding];
        [self.rtcManager setupLiveTranscodingUser];
        [self.rtcManager setupLiveBackgroundImage];
        
        [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Start];
        [[PLVWLogReporterManager sharedManager] reportWithEvent:@"startLiveStream" modul:PLVWELogModulLink information:nil patch:YES];
        return YES;
    }
}

/// 停止推流 (真正停止推流，非业务逻辑上的)
- (BOOL)stopPushStreamReal{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer, @"stop push stream real");
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"stopLiveStream" modul:PLVWELogModulLink information:nil patch:YES];
    [self requestForPushStreamAction:PLVRTCStreamerManager_MixAction_End];
    return YES;
}

/// 切换 ‘是否推流已开始’ 状态
- (void)changePushStreamState:(BOOL)toPushStreamStarted{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer, @"change push stream state with (state:%d)", toPushStreamStarted);
    
    BOOL needCallback = (toPushStreamStarted != self.pushStreamStarted);
    self.pushStreamStarted = toPushStreamStarted;
    
    if (toPushStreamStarted) {
        /// 开始计时器
        [self startPushStreamTimer];
        [self startSEITimer];
    }else{
        /// 停止计时器
        [self stopPushStreamTimer];
        [self stopSEITimer];
        [self stopReconnectingTimer];
        
        /// 重置数据
        [self resetData];
        [self changeSessionId:nil];
    }
    
    if (needCallback) { [self callbackForPushStreamStartedDidChanged]; }
}

/// 更新视频编码配置
- (PLVBRTCVideoEncoderConfiguration *)updateVideoEncoderConfiguration{
    PLVBRTCVideoEncoderConfiguration * videoEncoderConfiguration = self.rtcManager.currentVideoEncoderConfiguration;
    if (!videoEncoderConfiguration) { videoEncoderConfiguration = [[PLVBRTCVideoEncoderConfiguration alloc] init]; }
    
    PLVBLinkMicStreamQuality currentStreamQuality = self.streamQuality;
    PLVBLinkMicStreamScale currentStreamScale = self.streamScale;
    NSDictionary *clientParams = [PLVLiveVideoConfig sharedInstance].clientParams;
    
    PLVBRTCVideoOutputOrientationMode orientationMode;
    if (currentStreamScale == PLVBLinkMicStreamScale9_16 ||
        currentStreamScale == PLVBLinkMicStreamScale3_4) {
        orientationMode = PLVBRTCVideoOutputOrientationMode_Portrait;
    }else{
        orientationMode = PLVBRTCVideoOutputOrientationMode_Landscape;
    }
    
    CGSize videoResolution;
    NSInteger videoBitrate;
    if (currentStreamQuality == PLVBLinkMicStreamQuality1080P) {
        /// 1080P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution1440x1080;
            videoBitrate = 3620;
        }else{
            videoResolution = PLVBRTCVideoResolution1920x1080;
            videoBitrate = 4160;
        }
    }else if (currentStreamQuality == PLVBLinkMicStreamQuality720P) {
        /// 720P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution960x720;
            videoBitrate = 1820;
        }else{
            videoResolution = PLVBRTCVideoResolution1280x720;
            videoBitrate = 2260;
        }
    }else if (currentStreamQuality == PLVBLinkMicStreamQuality360P){
        /// 360P
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution480x360;
            videoBitrate = 640;
        }else{
            videoResolution = PLVBRTCVideoResolution640x360;
            videoBitrate = 800;
        }
    }else{
        /// 其他 (180P)
        if (currentStreamScale == PLVBLinkMicStreamScale4_3 ||
            currentStreamScale == PLVBLinkMicStreamScale3_4) {
            videoResolution = PLVBRTCVideoResolution240x180;
            videoBitrate = 240;
        }else{
            videoResolution = PLVBRTCVideoResolution320x180;
            videoBitrate = 280;
        }
    }
    videoEncoderConfiguration.videoResolution = videoResolution;
    videoEncoderConfiguration.videoBitrate = videoBitrate;
    videoEncoderConfiguration.videoFrameRate = PLV_SafeIntegerForDictKey(clientParams, @"fps") ?: 15;
    videoEncoderConfiguration.videoOutputOrientationMode = orientationMode;
    videoEncoderConfiguration.videoMirrorMode = self.localVideoStreamMirrorMode;
    
    if ([PLVLiveVideoConfig sharedInstance].clientPushStreamTemplateEnabled && [PLVFdUtil checkStringUseable:self.streamQualityLevel]) {
        for (PLVClientPushStreamTemplateVideoParams *videoParams in [PLVLiveVideoConfig sharedInstance].videoParams) {
            if ([PLVFdUtil checkStringUseable:videoParams.qualityLevel] && [videoParams.qualityLevel isEqualToString:self.streamQualityLevel]) {
                videoEncoderConfiguration.videoResolution = videoParams.videoResolution;
                videoEncoderConfiguration.videoBitrate = videoParams.videoBitrate;
                videoEncoderConfiguration.videoFrameRate = videoParams.videoFrameRate;
                self.currentVideoParams = videoParams;
                if (self.streamSourceType == PLVBRTCStreamSourceType_Screen) {
                    videoEncoderConfiguration.videoResolution = videoParams.screenResolution;
                    videoEncoderConfiguration.videoBitrate = videoParams.screenBitrate;
                    videoEncoderConfiguration.videoFrameRate = videoParams.screenFrameRate;
                }
                break;
            }
        }
    }
    
    videoEncoderConfiguration.videoQosPreference = self.videoQosPreference;
    return videoEncoderConfiguration;
}

#pragma mark Timer
/// 推流计时器
- (void)startPushStreamTimer{
    if (_pushStreamTimer) { [self stopPushStreamTimer]; }
    
    self.startPushStreamTimestamp = [self getCurrentTimestampSec];
    self.pushStreamTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(pushStreamTimerEvent) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.pushStreamTimer forMode:NSRunLoopCommonModes];
    [self.pushStreamTimer fire];
}

- (void)stopPushStreamTimer{
    [self.pushStreamTimer invalidate];
    self.pushStreamTimer = nil;

    if (self.startPushStreamTimestamp != 0) {
        [self callbackForCurrentPushStreamValidDuration];
        self.startPushStreamTimestamp = 0;
        [self callbackForCurrentPushStreamValidDuration];
    }
}

/// SEI计时器
- (void)startSEITimer{
    if (_SEITimer) { [self stopSEITimer]; }
    
    self.SEITimer = [NSTimer scheduledTimerWithTimeInterval:2.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(SEITimerEvent) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.SEITimer forMode:NSRunLoopCommonModes];
}

- (void)stopSEITimer{
    [self.SEITimer invalidate];
    self.SEITimer = nil;
}

/// 重连计时器
- (void)startReconnectingTimer{
    if (_reconnectingTimer) { [self stopReconnectingTimer]; }

    self.reconnectingTimestamp = [self getCurrentTimestampSec];
    self.reconnectingTimer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(reconnectingTimerEvent) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.reconnectingTimer forMode:NSRunLoopCommonModes];
}

- (void)stopReconnectingTimer{
    [self.reconnectingTimer invalidate];
    self.reconnectingTimer = nil;
    
    if (self.reconnectingTimestamp > 0) {
        if (_pushStreamStarted) {
            _reconnectingDuration += self.reconnectingThisTimeDuration;
        }
    }
    
    if (self.reconnectingTimestamp != 0) {
        [self callbackForCurrentReconnectingThisTimeDuration];
        self.reconnectingTimestamp = 0;
        [self callbackForCurrentReconnectingThisTimeDuration];
    }
}

/// 混流Action重试计时器
- (void)startMixActionRetryTimer {
    if (_mixActionRetryTimer) { [self stopMixActionRetryTimer]; }
    
    self.mixActionRetryTimer = [NSTimer scheduledTimerWithTimeInterval:2.0 target:[PLVFWeakProxy proxyWithTarget:self] selector:@selector(mixActionRetryTimerEvent) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:self.mixActionRetryTimer forMode:NSRunLoopCommonModes];
}

- (void)stopMixActionRetryTimer {
    if (_mixActionRetryTimer) {
        [self.mixActionRetryTimer invalidate];
        self.mixActionRetryTimer = nil;
    }
}

- (NSTimeInterval)getCurrentTimestampSec{
    return [[NSDate date] timeIntervalSince1970];
}

#pragma mark Socket
- (void)emitSocketMessage_OPEN_MICROPHONE_open:(BOOL)open videoType:(BOOL)videoType emitCompleteBlock:(void (^)(BOOL openSuccess))emitCompleteBlock{
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {
        
        if (![PLVFdUtil checkStringUseable:self.channelId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE failed with 【parma illegal】 (channelId:%@)", self.channelId);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"EVENT"] = @"OPEN_MICROPHONE";
        jsonDict[@"roomId"] = [NSString stringWithFormat:@"%@",self.channelId];
        jsonDict[@"teacherId"] = [NSString stringWithFormat:@"%@",self.channelId];
        jsonDict[@"status"] = open ? @"open" : @"close";
        jsonDict[@"type"] = videoType ? @"video" : @"audio";
        
        __weak typeof(self) weakSelf = self;
        [[PLVSocketManager sharedManager] emitEvent:@"message" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                weakSelf.channelLinkMicOpen = open;
                if (open || weakSelf.shouldApplyLinkMicNewStrategy) {
                    weakSelf.channelLinkMicMediaType = (videoType ? PLVChannelLinkMicMediaType_Video : PLVChannelLinkMicMediaType_Audio);
                }else{
                    weakSelf.channelLinkMicMediaType = PLVChannelLinkMicMediaType_Unknown;
                }
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(YES); }) }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE failed with 【callback illegal】 (ack:%@)", ackArray);
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            }
        }];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE failed with  (current state:%lu)", (unsigned long)[PLVSocketManager sharedManager].status);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
    }
}

- (BOOL)emitSocketMessage_OPEN_MICROPHONE_removeAllWithCallback:(void (^ _Nullable)(NSArray * _Nonnull))callback {
    if (!([PLVSocketManager sharedManager].login &&
          [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected)) {
        return NO;
    }
    
    if (![PLVFdUtil checkStringUseable:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE remove all failed with 【parma illegal】 (channelId:%@)", self.channelId);
        return NO;
    }
    NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
    jsonDict[@"EVENT"] = @"OPEN_MICROPHONE";
    jsonDict[@"roomId"] = [NSString stringWithFormat:@"%@",self.channelId];
    jsonDict[@"teacherId"] = [NSString stringWithFormat:@"%@",self.channelId];
    jsonDict[@"status"] = @"removeAll";
    jsonDict[@"type"] = self.channelLinkMicMediaType == PLVChannelLinkMicMediaType_Video ? @"video" : @"audio";
    BOOL success = [[PLVSocketManager sharedManager] emitEvent:@"message" content:jsonDict timeout:5.0 callback:callback];
    return success;
}

- (void)emitSocketMessage_OPEN_MICROPHONE_closeRemoteUser_userDict:(NSDictionary *)userDict emitCompleteBlock:(void (^)(BOOL openSuccess))emitCompleteBlock{
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {
        
        if (![PLVFdUtil checkStringUseable:self.channelId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE close remote user failed with 【param illegal】(channelId:%@)", self.channelId);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        if (![PLVFdUtil checkDictionaryUseable:userDict]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE close remote user failed with 【param illegal】(userDict:%@)", userDict);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        NSString * typeString = @"";
        if (self.channelLinkMicMediaType == PLVChannelLinkMicMediaType_Audio) {
            typeString = @"audio";
        }else if (self.channelLinkMicMediaType == PLVChannelLinkMicMediaType_Video){
            typeString = @"video";
        }else{
            typeString = @"video"; /// 210522，Lincal，对齐网页开播，在未开启连麦功能情况下，也写作 @"video"
        }
        NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"EVENT"] = @"OPEN_MICROPHONE";
        jsonDict[@"roomId"] = [NSString stringWithFormat:@"%@",self.channelId];
        jsonDict[@"status"] = @"close";
        jsonDict[@"type"] = typeString;
        jsonDict[@"userId"] = [NSString stringWithFormat:@"%@",userDict[@"userId"]];

        [[PLVSocketManager sharedManager] emitEvent:@"message" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(YES); }) }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE close remote user failed with 【callback illegal】(ack:%@)", ackArray);
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            }
        }];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit OPEN_MICROPHONE close failed with  (current state:%lu)", (unsigned long)[PLVSocketManager sharedManager].status);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
    }
}

- (void)emitSocketMessage_joinResponse_userDict:(NSDictionary *)userDict raiseHand:(NSString *)raiseHand emitCompleteBlock:(void (^)(BOOL openSuccess))emitCompleteBlock{
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {
        
        if (![PLVFdUtil checkStringUseable:self.channelId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit joinResponse failed with 【param illegal】(channelId:%@)", self.channelId);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        if (![PLVFdUtil checkDictionaryUseable:userDict]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit joinResponse failed with 【param illegal】(userDict:%@)", userDict);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"roomId"] = [NSString stringWithFormat:@"%@",self.channelId];
        jsonDict[@"value"] = @"1";
        jsonDict[@"toEmitAll"]  = @"1";
        
        NSString * userType = userDict[@"userType"];
        NSMutableDictionary * userMutableDict = [NSMutableDictionary dictionaryWithDictionary:userDict];
        // 嘉宾、观众可以举手申请连麦
        if ([userType isEqualToString:@"guest"] ||
            [userType isEqualToString:@"slice"] ||
            [userType isEqualToString:@"student"]) {
            if ([PLVFdUtil checkStringUseable:raiseHand]) {
                // 举手用户不需要 needAnswer @(0) 未举手用户需要 needAnswer @(1)
                jsonDict[@"needAnswer"] = [raiseHand isEqualToString:@"0"] ? @(1) : @(0);
            }
        }
        
        jsonDict[@"user"] = userMutableDict;

        [[PLVSocketManager sharedManager] emitEvent:@"joinResponse" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(YES); }) }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit joinResponse failed with 【callback illegal】(ack:%@)", ackArray);
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            }
        }];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit joinResponse failed with  (current state:%lu)", (unsigned long)[PLVSocketManager sharedManager].status);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
    }
}

- (void)emitSocketMessage_MUTE_USER_MEDIA_userDict:(NSDictionary *)userDict videoType:(BOOL)videoType mute:(BOOL)mute emitCompleteBlock:(void (^)(BOOL openSuccess))emitCompleteBlock{
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {
        
        if (![PLVFdUtil checkStringUseable:self.channelId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit MUTE_USER_MEDIA failed with 【param illegal】(channelId:%@)", self.channelId);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        if (![PLVFdUtil checkDictionaryUseable:userDict]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit MUTE_USER_MEDIA failed with 【param illegal】(userDict:%@)", userDict);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"EVENT"] = @"MUTE_USER_MEDIA";
        jsonDict[@"type"] = videoType ? @"video" : @"audio";
        jsonDict[@"mute"] = mute ? @(YES) : @(NO);
        jsonDict[@"teacherId"] = [NSString stringWithFormat:@"%@",self.channelId];
        jsonDict[@"userId"] = [NSString stringWithFormat:@"%@",userDict[@"userId"]];
        jsonDict[@"uid"] = [NSString stringWithFormat:@"%@",userDict[@"uid"]];
        jsonDict[@"sessionId"] = [NSString stringWithFormat:@"%@",self.sessionId];
        
        [[PLVSocketManager sharedManager] emitEvent:@"message" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(YES); }) }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit MUTE_USER_MEDIA failed with 【callback illegal】(ack:%@)", ackArray);
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            }
        }];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit MUTE_USER_MEDIA failed with  (current state:%lu)", (unsigned long)[PLVSocketManager sharedManager].status);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
    }
}

- (void)emitSocketMessage_addGuestFromSIP_userRTCId:(NSString *)userRTCId emitCompleteBlock:(void (^)(BOOL emitSuccess))emitCompleteBlock{
    if ([PLVSocketManager sharedManager].login &&
        [PLVSocketManager sharedManager].status == PLVSocketConnectStatusConnected) {

        if (![PLVFdUtil checkStringUseable:userRTCId]) {
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit addGuestFromSIP failed with 【param illegal】(userRTCId:%@)", userRTCId);
            if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            return;
        }
        
        NSMutableDictionary * jsonDict = [NSMutableDictionary dictionary];
        jsonDict[@"userId"] = [NSString stringWithFormat:@"%@",userRTCId];
        jsonDict[@"nick"] = PLVFDLocalizableString(@"PLVRTCStreamerManagerSIPGuestNick");
        jsonDict[@"pic"] = kPLVRTCStreamerManager_SIP_GuestPic;
        
        [[PLVSocketManager sharedManager] emitEvent:@"addGuestFromSIP" content:jsonDict timeout:5.0 callback:^(NSArray *ackArray) {
            if ([PLVFdUtil checkArrayUseable:ackArray]) {
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(YES); }) }
            }else{
                PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit addGuestFromSIP failed with 【callback illegal】(ack:%@)", ackArray);
                if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
            }
        }];
    }else{
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"emit addGuestFromSIP failed with  (current state:%lu)", (unsigned long)[PLVSocketManager sharedManager].status);
        if (emitCompleteBlock) { plv_dispatch_main_async_safe(^{ emitCompleteBlock(NO); }) }
    }
}

#pragma mark Net Request
- (void)requestForSetupStream{
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI notifyStreamModeWithChannelId:self.channelId stream:self.stream sessionId:@"" videowidth:self.rtcManager.videoCDNWidth videoheight:self.rtcManager.videoCDNHeight continueLastLive:self.isContinue success:^(NSString * _Nonnull responseContent) {
        [weakSelf switchRoleTypeTo:PLVBLinkMicRoleBroadcaster];
        [weakSelf startPushStreamReal];
    } failure:^(NSError * _Nonnull error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for setup stream failed with 【%@】", error);
        NSError * finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_PushStreamFailedSetupStreamError errorDescription:nil];
        finalError = PLVErrorWithUnderlyingError(finalError, error);
        [weakSelf callbackForDidOccurError:finalError];
    }];
}
- (void)requestForPushStreamAction:(NSString *)action {
    [self requestForPushStreamAction:action callback:nil];
}

- (void)requestForPushStreamAction:(NSString *)action callback:(void (^)(BOOL failed))callback {
    [self requestForPushStreamAction:action retry:NO callback:callback];
}

- (void)requestForPushStreamAction:(NSString *)action retry:(BOOL)retry callback:(void (^)(BOOL failed))callback {
    if (retry && !self.mixActionCompleted) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"retry request for push stream action failed with last request push stream no completed", action);
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:action]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with【action %@】", action);
        if (callback) {
            callback(YES);
        }
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:self.channelId]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with【channelId %@】", self.channelId);
        if (callback) {
            callback(YES);
        }
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:self.rtmpUrl]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with【rtmpUrl %@】", self.rtmpUrl);
        if (callback) {
            callback(YES);
        }
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:self.stream]) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with【stream %@】", self.stream);
        if (callback) {
            callback(YES);
        }
        return;
    }
    
    if (!retry) {
        // 不是重试的情况下，需要销毁重试定时器
        [self stopMixActionRetryTimer];
    }
    
    if ([action isEqualToString:PLVRTCStreamerManager_MixAction_Update] && !retry) {
        NSTimeInterval minTimeInterval = 2.0;
        NSTimeInterval passedTimeInterval = [self getCurrentTimestampSec] - self.updateActionDate;
        if (passedTimeInterval >= minTimeInterval) {
            self.updateActionDate = [self getCurrentTimestampSec];
        }else{
            if (!self.waitingNextActionUpdate) {
                NSTimeInterval leftTimeInterval = minTimeInterval - passedTimeInterval;
                if (leftTimeInterval <= 0) { leftTimeInterval = 0; }
                self.waitingNextActionUpdate = YES;
                __weak typeof(self) weakSelf = self;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(leftTimeInterval * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    weakSelf.waitingNextActionUpdate = NO;
                    [weakSelf requestForPushStreamAction:PLVRTCStreamerManager_MixAction_Update callback:callback];
                });
            }
            return;
        }
    }
    
    NSMutableDictionary * mixInfo = [[NSMutableDictionary alloc] init];
    mixInfo[@"action"] = action;
    mixInfo[@"roomId"] = self.channelId;
    mixInfo[@"mixLayoutType"] = @((self.mixLayoutType >= 1 && self.mixLayoutType <= 3) ? self.mixLayoutType : 1);
    mixInfo[@"publishCdnParam"] = @{@"cdnUrl" : self.rtmpUrl};
    
    NSMutableDictionary * outputParam = [[NSMutableDictionary alloc] init];
    outputParam[@"streamId"] = self.stream;
    outputParam[@"pureAudioStream"] = @0;
    mixInfo[@"outputParam"] = outputParam;

    NSMutableDictionary * encodeParam = [[NSMutableDictionary alloc] init];
    encodeParam[@"audioSampleRate"] = @48000;
    encodeParam[@"audioBitrate"] = @64;
    encodeParam[@"audioChannels"] = @1;
    encodeParam[@"videoWidth"] = @(self.rtcManager.videoCDNWidth);
    encodeParam[@"videoHeight"] = @(self.rtcManager.videoCDNHeight);
    encodeParam[@"videoBitrate"] = @(self.rtcManager.currentVideoEncoderConfiguration.videoBitrate);
    encodeParam[@"videoFramerate"] = @(self.rtcManager.currentVideoEncoderConfiguration.videoFrameRate);
    encodeParam[@"videoGop"] = @2;
    if ([PLVLiveVideoConfig sharedInstance].clientPushStreamTemplateEnabled) {
        PLVClientPushStreamTemplateAudioParams *audioParams = [PLVLiveVideoConfig sharedInstance].audioParams;
        encodeParam[@"audioSampleRate"] = @(audioParams.audioSamplerate);
        encodeParam[@"audioBitrate"] = @(audioParams.audioBitrate);
        encodeParam[@"audioChannels"] = @(audioParams.audioChannels);
        encodeParam[@"videoGop"] = @(self.currentVideoParams.videoGop);
        if (self.streamSourceType == PLVBRTCStreamSourceType_Screen) {
            encodeParam[@"videoGop"] = @(self.currentVideoParams.screenGop);
        }
    }
    encodeParam[@"backgroundColor"] = @0;
    encodeParam[@"audioCodec"] = @0;
    mixInfo[@"encodeParam"] = encodeParam;

    NSMutableArray * mixUserDictArray = [[NSMutableArray alloc] init];
    if ([PLVFdUtil checkArrayUseable:self.mixUserList]) {
        for (PLVRTCStreamerMixUser * mixUser in self.mixUserList) {
            if ([PLVFdUtil checkStringUseable:mixUser.userRTCId]) {
                NSMutableDictionary * mixUserDict = [[NSMutableDictionary alloc] init];
                if (mixUser.streamType == PLVRTCStreamerMixUserStreamType_Screen) {
                    mixUserDict[@"streamType"] = self.rtcManager.rtcType == PLVBLinkMicRTCType_TX ? @0 : @1;
                } else {
                    mixUserDict[@"streamType"] = @0;
                }
                mixUserDict[@"userId"] = mixUser.userRTCId;
                if (mixUser.renderMode >= PLVRTCStreamerMixUserRenderMode_Fill &&
                    mixUser.renderMode <= PLVRTCStreamerMixUserRenderMode_FitBlackBase &&
                    self.rtcManager.rtcType != PLVBLinkMicRTCType_BD) {
                    mixUserDict[@"renderMode"] = @(mixUser.renderMode);
                }else{
                    mixUserDict[@"renderMode"] = @(2);
                }
                mixUserDict[@"mixInputType"] = @(mixUser.inputType);
                if ([mixUser.userRTCId hasSuffix:@"_sip"]) {
                    mixUserDict[@"hidden"] = @(YES);
                }
                mixUserDict[@"nickname"] = [PLVFdUtil checkStringUseable:mixUser.nickname] ? mixUser.nickname : @"";
                [mixUserDictArray addObject:mixUserDict];
            }else{
                continue;
            }
        }
    }
    
    /// 若最终数组仍为空
    if (![PLVFdUtil checkArrayUseable:mixUserDictArray]) {
        NSMutableDictionary * mixUserDict = [[NSMutableDictionary alloc] init];
        mixUserDict[@"streamType"] = @0;
        mixUserDict[@"userId"] = self.channelId;
        mixUserDict[@"renderMode"] = @1;
        mixUserDict[@"mixInputType"] = self.localCameraOpen ? @0 : @2;
        mixUserDict[@"nickname"] = @"";
        [mixUserDictArray addObject:mixUserDict];
    }
    mixInfo[@"userList"] = mixUserDictArray;

    self.lastMixAction = action;
    self.mixActionCompleted = NO;
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI requestPushStreamMixActionWitMixInfo:mixInfo success:^(NSDictionary * _Nonnull responseDict) {
        weakSelf.mixActionCompleted = YES;
        NSDictionary *data = responseDict[@"data"];
        BOOL resSuccess = PLV_SafeBoolForDictKey(data, @"res");
        if (resSuccess) {
            [weakSelf stopMixActionRetryTimer];
            if ([action isEqualToString:PLVRTCStreamerManager_MixAction_Start]) {
                [weakSelf plvbLinkMicManager:weakSelf.rtcManager streamPublishedResult:YES];
            }
        } else {
            // RTC服务 请求失败
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with 【%@】", responseDict);
            NSString *message = PLV_SafeStringForDictKey(responseDict, @"message");
            NSError *finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_PushStreamFailedActionError errorDescription:message];
            [weakSelf callbackForDidOccurError:finalError];
            [[PLVWLogReporterManager sharedManager] reportWithEvent:@"requestPushStreamAction" modul:PLVWELogModulLink information:finalError patch:YES];
            
            if (!weakSelf.mixActionRetryTimer &&
                ![action isEqualToString:PLVRTCStreamerManager_MixAction_End]){
                [weakSelf startMixActionRetryTimer];
            }
            
            if (callback) {
                callback(YES);
            }
        }
    } failure:^(NSError * _Nonnull error) {
        // 接口请求失败
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for push stream action failed with 【%@】", error);
        NSError * finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_PushStreamFailedActionError errorDescription:nil];
        finalError = PLVErrorWithUnderlyingError(finalError, error);
        [weakSelf callbackForDidOccurError:finalError];
        [[PLVWLogReporterManager sharedManager] reportWithEvent:@"requestPushStreamAction" modul:PLVWELogModulLink information:finalError patch:YES];

        weakSelf.mixActionCompleted = YES;
        if (!weakSelf.mixActionRetryTimer &&
            ![action isEqualToString:PLVRTCStreamerManager_MixAction_End]){
            [weakSelf startMixActionRetryTimer];
        }
        
        if (callback) {
            callback(YES);
        }
    }];
    
    if ([action isEqualToString:PLVRTCStreamerManager_MixAction_End]){
        [self plvbLinkMicManagerStreamUnpublished:self.rtcManager];
    }
}

- (void)requestForSessionId{
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI requestChannelSessionIdWithChannelId:self.channelId stream:self.stream success:^(NSString * _Nonnull responseContent) {
        /// 更新值
        BOOL sessionIdUsable = [weakSelf changeSessionId:responseContent];
        if (sessionIdUsable) {
            /// 切换 ‘是否推流已开始’ 状态
            [weakSelf changePushStreamState:YES];
            [[PLVWLogReporterManager sharedManager] reportWithEvent:@"onLiveStreamingStart" modul:PLVWELogModulLink information:nil patch:YES];
        }else{
            PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for sessionID failed with【result:%@】", responseContent);
            NSError * finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_PushStreamFailedSessionIllegal errorDescription:nil];
            [weakSelf callbackForDidOccurError:finalError];
        }
    } failure:^(NSError * _Nonnull error) {
        PLV_LOG_ERROR(PLVConsoleLogModuleTypeStreamer, @"request for sessionID failed with【%@】", error);
        NSError * finalError = [weakSelf errorWithCode:PLVRTCStreamerManagerErrorCode_PushStreamFailedSessionGetError errorDescription:nil];
        finalError = PLVErrorWithUnderlyingError(finalError, error);
        [weakSelf callbackForDidOccurError:finalError];
    }];
}

#pragma mark Callback
/// RTC房间事件
- (void)callbackForLocalUserJoinRTCChannelComplete{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:localUserJoinRTCChannelComplete:)]) {
            [self.delegate plvRTCStreamerManager:self localUserJoinRTCChannelComplete:self.channelId];
        }
    })
}

- (void)callbackForLocalUserLeaveRTCChannelComplete{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:localUserLeaveRTCChannelComplete:)]) {
            [self.delegate plvRTCStreamerManager:self localUserLeaveRTCChannelComplete:self.channelId];
        }
    })
}

- (void)callbackForLocalUserLeaveRTCChannelByServerComplete {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:localUserLeaveRTCChannelByServerComplete:)]) {
            [self.delegate plvRTCStreamerManager:self localUserLeaveRTCChannelByServerComplete:self.channelId];
        }
    })
}

- (void)callbackForSessionIdDidChanged{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvRTCStreamerManager:sessionIdDidChanged:)]) {
            [self.delegate plvRTCStreamerManager:self sessionIdDidChanged:self.sessionId];
        }
    })
}

- (void)callbackForNetworkQualityDidChanged{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:networkQualityDidChanged:)]) {
            [self.delegate plvRTCStreamerManager:self networkQualityDidChanged:self.networkQuality];
        }
    })
}

- (void)callbackForLocalNetworkQualityChanged {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:localNetworkQualityDidChanged:)]) {
            [self.delegate plvRTCStreamerManager:self localNetworkQualityDidChanged:self.localNetworkQuality];
        }
    })
}

- (void)callbackForStatistics:(PLVBRTCStatistics *)statistics {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:rtcStatistics:)]) {
            PLVRTCStatistics *statis = [[PLVRTCStatistics alloc] init];
            statis.rtt = statistics.rtt;
            statis.upLoss = statistics.upLoss;
            statis.downLoss = statistics.downLoss;
            [self.delegate plvRTCStreamerManager:self rtcStatistics:statis];
        }
    })
}

/// CDN推流事件
- (void)callbackForPushStreamStartedDidChanged{
    plv_dispatch_main_async_safe(^{
        if ([self.delegate respondsToSelector:@selector(plvRTCStreamerManager:pushStreamStartedDidChanged:)]) {
            [self.delegate plvRTCStreamerManager:self pushStreamStartedDidChanged:self.pushStreamStarted];
        }
    })
}

- (void)callbackForCurrentPushStreamValidDuration{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:currentPushStreamValidDuration:)]) {
            [self.delegate plvRTCStreamerManager:self currentPushStreamValidDuration:self.pushStreamValidDuration];
        }
    })
}

- (void)callbackForCurrentReconnectingThisTimeDuration{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:currentReconnectingThisTimeDuration:)]) {
            [self.delegate plvRTCStreamerManager:self currentReconnectingThisTimeDuration:self.reconnectingThisTimeDuration];
        }
    })
}

/// 本地用户硬件事件
- (void)callbackForLocalVoiceValue:(CGFloat)localVoiceValue receivedLocalAudibleVoice:(BOOL)voiceAudible{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:localVoiceValue:receivedLocalAudibleVoice:)]) {
            [self.delegate plvRTCStreamerManager:self localVoiceValue:localVoiceValue receivedLocalAudibleVoice:voiceAudible];
        }
    })
}

/// 远端用户事件
- (void)callbackForRemoteUser:(NSString *)userRTCId audioMuted:(BOOL)audioMuted{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:remoteUser:audioMuted:)]) {
            [self.delegate plvRTCStreamerManager:self remoteUser:userRTCId audioMuted:audioMuted];
        }
    })
}

- (void)callbackForRemoteUser:(NSString *)userRTCId videoMuted:(BOOL)audioMuted{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:remoteUser:videoMuted:)]) {
            [self.delegate plvRTCStreamerManager:self remoteUser:userRTCId videoMuted:audioMuted];
        }
    })
}

/// 全部用户事件
- (void)callbackForReportAudioVolumeOfSpeakers:(NSDictionary<NSString *, NSNumber *> * _Nonnull)volumeDict{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:reportAudioVolumeOfSpeakers:)]) {
            [self.delegate plvRTCStreamerManager:self reportAudioVolumeOfSpeakers:volumeDict];
        }
    })
}

/// 管理器状态事件
- (void)callbackForDidOccurError:(NSError *)error{
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:didOccurError:)]) {
            [self.delegate plvRTCStreamerManager:self didOccurError:error];
        }
    })
}

/// 美颜错误回调
- (void)callbackForBeautyDidInitWithResult:(int)result {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:beautyDidInitWithResult:)]) {
            [self.delegate plvRTCStreamerManager:self beautyDidInitWithResult:result];
        }
    })
}

/// 美颜错误回调
- (void)callbackForBeautyProcessDidOccurError:(NSError *)error {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:beautyProcessDidOccurError:)]) {
            [self.delegate plvRTCStreamerManager:self beautyProcessDidOccurError:error];
        }
    })
}

- (void)callbackForUpdateMixLayoutDidOccurError:(PLVRTCStreamerMixLayoutType)type {
    plv_dispatch_main_async_safe(^{
        if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:updateMixLayoutDidOccurError:)]) {
            [self.delegate plvRTCStreamerManager:self updateMixLayoutDidOccurError:type];
        }
    })
}

#pragma mark - [ Event ]
#pragma mark Timer
- (void)pushStreamTimerEvent{
    if (self.startPushStreamTimestamp <= 0) { return; }
    if (self.pushStreamStarted) {
        [self callbackForCurrentPushStreamValidDuration];
    }
}

- (void)SEITimerEvent{
    if (self.pushStreamStarted) {
        NSString * seiString = [NSString stringWithFormat:@"%.0f", floor(self.pushStreamTotalDuration * 1000.0)];
        [self.rtcManager setupLiveTranscodingExtraInfo:seiString];
    }
}

- (void)reconnectingTimerEvent{
    if (self.reconnectingTimestamp <= 0) { return; }
    if (self.pushStreamStarted) {
        [self callbackForCurrentReconnectingThisTimeDuration];
    }
}

- (void)mixActionRetryTimerEvent{
    [self requestForPushStreamAction:self.lastMixAction retry:YES callback:nil];
}

#pragma mark Notification
/// 回到前台
- (void)didBecomeActive:(NSNotification *)notification{
    if (self.enterBackgroundTimestamp > 0) {
        if (self.pushStreamStarted) {
            NSTimeInterval backToActiveTimeInterval = [self getCurrentTimestampSec] - self.enterBackgroundTimestamp;
            _enterBackgroundDuration += backToActiveTimeInterval;
        }
    }
    self.enterBackgroundTimestamp = 0;
    if (self.publishStreamSourceType == PLVBRTCStreamSourceType_Screen) {
        return;
    }

    if (self.pushStreamStarted && self.originalLocalCameraOpen && !self.localCameraOpen) {
        [self openLocalUserCamera:YES completion:nil];
    }
}

/// 退至后台
- (void)didEnterBackground:(NSNotification *)notification{
    self.enterBackgroundTimestamp = [self getCurrentTimestampSec];
    if (self.publishStreamSourceType == PLVBRTCStreamSourceType_Screen) {
        /// 当流的源类型为屏幕时，退出后台不需要关闭 本地用户 的摄像头
        return;
    }
    
    self.originalLocalCameraOpen = self.localCameraOpen;
    if (self.pushStreamStarted && self.localCameraOpen) {
        [self openLocalUserCamera:NO completion:nil];
    }
}


#pragma mark - [ Delegate ]
#pragma mark PLVBLinkMicManagerDelegate
- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didOccurError:(NSError *)error{
    PLVBLinkMicRTCType rtcType = self.rtcManager.rtcType;
    NSString * rtcTypeName = @"unknown";
    if (rtcType == PLVBLinkMicRTCType_AG) {
        rtcTypeName = @"ARTC";
    }else if (rtcType == PLVBLinkMicRTCType_UC) {
        rtcTypeName = @"URTC";
    }else if (rtcType == PLVBLinkMicRTCType_ZE) {
        rtcTypeName = @"ZRTC";
    }else if (rtcType == PLVBLinkMicRTCType_TX) {
        rtcTypeName = @"TRTC";
    }else if (rtcType == PLVBLinkMicRTCType_BD) {
        rtcTypeName = @"VOLC";
    }
    NSString *eventString = [NSString stringWithFormat:@"linkMicdidOccurError:%@,%ld",rtcTypeName, error.code];
    [[PLVWLogReporterManager sharedManager] reportWithEvent:eventString modul:PLVWELogModulLink information:nil];
    
    PLVRTCStreamerManagerErrorCode errorCode = PLVRTCStreamerManagerErrorCode_RTCManagerError;
    /// 错误细分
    NSInteger lastErrorCode = PLVErrorLastErrorCode(error);
    if (lastErrorCode == 1012) { /// 仅ARTC会有此错误码
        errorCode = PLVRTCStreamerManagerErrorCode_RTCManagerErrorStartAudioFailed;
    }
    /// 生成错误对象，对外回调
    NSError * finalError = [self errorWithCode:errorCode errorDescription:nil];
    finalError = PLVErrorWithUnderlyingError(finalError, error);
    [self callbackForDidOccurError:finalError];
}

- (void)plvbLinkMicManagerTokenExpires:(PLVBLinkMicManager *)manager{
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"onTokenExpired" modul:PLVWELogModulLink information:nil];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager rtcConnectionStateDidChanged:(PLVBLinkMicConnectionStateType)connectionState{
    if (connectionState == PLVBLinkMicConnectionStateDisconnected ||
        connectionState == PLVBLinkMicConnectionStateReconnecting ||
        connectionState == PLVBLinkMicConnectionStateFailed) {
        if (!_reconnectingTimer && _pushStreamStarted) {
            [self startReconnectingTimer];
        }
    }else{
        [self stopReconnectingTimer];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager joinRTCChannelComplete:(NSString *)channelId uid:(NSString *)uid{
    if (self.joinChannelSuccessBlock) {
        self.joinChannelSuccessBlock();
        self.joinChannelSuccessBlock = nil;
    }
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"joinChannel" modul:PLVWELogModulLink information:nil patch:YES];
    [self callbackForLocalUserJoinRTCChannelComplete];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager leaveRTCChannelComplete:(NSString *)channelId{
    [[PLVWLogReporterManager sharedManager] reportWithEvent:@"leaveChannel" modul:PLVWELogModulLink information:nil patch:YES];
    [self callbackForLocalUserLeaveRTCChannelComplete];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager leaveRTCChannelByServerComplete:(NSString *)channelId {
    [self callbackForLocalUserLeaveRTCChannelByServerComplete];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didJoinedOfUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer, @"other join rtc channel with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvRTCStreamerManager:didJoinedOfUid:)]) {
        [self.delegate plvRTCStreamerManager:self didJoinedOfUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didOfflineOfUid:(NSString *)uid{
    PLV_LOG_INFO(PLVConsoleLogModuleTypeStreamer, @"other leave rtc channel with (uid:%@)", uid);
    if ([self.delegate respondsToSelector:@selector(plvRTCStreamerManager:didOfflineOfUid:)]) {
        [self.delegate plvRTCStreamerManager:self didOfflineOfUid:uid];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager networkQualityDidChanged:(PLVBLinkMicNetworkQuality)networkQuality{
    [self callbackForNetworkQualityDidChanged];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager localNetworkQualityDidChanged:(PLVBRTCNetworkQuality)networkQuality {
    [self callbackForLocalNetworkQualityChanged];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager rtcStatistics:(PLVBRTCStatistics *)statistics {
    [self callbackForStatistics:statistics];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager streamPublishedResult:(BOOL)sucess{
    if (sucess) {
        [self requestForSessionId];
    }
}

- (void)plvbLinkMicManagerStreamUnpublished:(PLVBLinkMicManager *)manager{
    [self changePushStreamState:NO];
}

- (void)plvbLinkMicManagerCameraDidReady:(PLVBLinkMicManager *)manager{

}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didLocalVideoMuted:(BOOL)videoMuted{

}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didVideoMuted:(BOOL)muted byUid:(NSString *)uid{
    [self callbackForRemoteUser:uid videoMuted:muted];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didAudioMuted:(BOOL)muted byUid:(NSString *)uid{
    [self callbackForRemoteUser:uid audioMuted:muted];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager * _Nonnull)manager didNewStreamJoinRoomOfUid:(NSString *)uid{
    if ([PLVFdUtil checkStringUseable:uid] && [uid hasSuffix:@"_sip"]) {
        [self emitSocketMessage_addGuestFromSIP_userRTCId:uid emitCompleteBlock:^(BOOL emitSuccess) { }];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager localVoiceValue:(CGFloat)localVoiceValue receivedLocalAudibleVoice:(BOOL)voiceAudible{
    [self callbackForLocalVoiceValue:localVoiceValue receivedLocalAudibleVoice:voiceAudible];
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager reportAudioVolumeOfSpeakers:(NSDictionary<NSString *, NSNumber *> *)volumeDict{
    [self callbackForReportAudioVolumeOfSpeakers:volumeDict];
}

- (void)plvbLinkMicManagerCanSetupLocalHardwareDefaultState:(PLVBLinkMicManager *)manager{

}

- (void)plvbLinkMicManagerDidScreenCaptureStarted:(PLVBLinkMicManager *)manager {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManagerDidScreenCaptureStarted:)]) {
        [self.delegate plvRTCStreamerManagerDidScreenCaptureStarted:self];
    }
}

- (void)plvbLinkMicManager:(PLVBLinkMicManager *)manager didScreenCaptureStopedReason:(PLVBRTCScreenCaptureFinishedReason)reason {
    if (self.delegate && [self.delegate respondsToSelector:@selector(plvRTCStreamerManager:didScreenCaptureStopedReason:)]) {
        [self.delegate plvRTCStreamerManager:self didScreenCaptureStopedReason:reason];
    }
}

- (NSString *)plvbLinkMicManagerGetUCStreamAuthority:(PLVBLinkMicManager *)manager{
    return @"All";
}

- (int)plvbLinkMicManager:(PLVBLinkMicManager *)manager
captureVideoFrameTextureId:(GLuint)sourceTextureId videoFrameWidth:(uint32_t)sourceFrameWidth
         videoFrameHeight:(uint32_t)sourceFrameHeight
      videoFrameTimeStamp:(uint64_t)sourceFrameTimeStamp
processedVideoFrameTextureId:(GLuint)processedTextureId {
    if (!self.beautyManager) {
        return -1;
    }
    CGFloat rotate = 0.0;
    UIDeviceOrientation orientation = [[UIDevice currentDevice] orientation];
    switch (orientation) {
        case UIDeviceOrientationPortrait:
            rotate = 0.0;
            break;
        case UIDeviceOrientationLandscapeLeft:
            rotate = 1;
            break;
        case UIDeviceOrientationLandscapeRight:
            rotate = 3;
            break;
        case UIDeviceOrientationPortraitUpsideDown:
            rotate = 2;
            break;
        default:
            break;
    }
    /// 美颜数据同步处理，并返回给RTC
    return [self.beautyManager processTexture:sourceTextureId outputTexture:processedTextureId width:sourceFrameWidth height:sourceFrameHeight rotate:rotate timeStamp:sourceFrameTimeStamp];
}

- (int)plvbLinkMicManager:(PLVBLinkMicManager *)manager
captureVideoFramePixelBuffer:(CVPixelBufferRef)inputPixelBuffer
          videoFrameWidth:(uint32_t)sourceFrameWidth
         videoFrameHeight:(uint32_t)sourceFrameHeight
      videoFrameTimeStamp:(uint64_t)sourceFrameTimeStamp
processedVideoFramePixelBuffer:(CVPixelBufferRef)outputPixelBuffer{
    if (!self.beautyManager) {
        return -1;
    }
    CGFloat rotate = 0.0;
    UIDeviceOrientation orientation = [[UIDevice currentDevice] orientation];
    switch (orientation) {
        case UIDeviceOrientationPortrait:
            rotate = 0.0;
            break;
        case UIDeviceOrientationLandscapeLeft:
            rotate = 1;
            break;
        case UIDeviceOrientationLandscapeRight:
            rotate = 3;
            break;
        case UIDeviceOrientationPortraitUpsideDown:
            rotate = 2;
            break;
        default:
            break;
    }
    
    // opengl 上下文环境 是否需要设置 ？？
    self.beautyManager.eaglContext = self.rtcManager.eaglContext;
    return [self.beautyManager processPixelBuffer:inputPixelBuffer outBuf:outputPixelBuffer rotate:rotate timeStamp:sourceFrameTimeStamp];
}

#pragma mark PLVBeautyManagerDelegate
-(void)plvBeautyManager:(PLVBeautyManager *)manager didOccurError:(NSError *)error {
    [self callbackForBeautyProcessDidOccurError:error];
}

#pragma mark PLVBeautyResourceManagerDelegate
- (void)plvBeautyResourceManagerUpdateResourceInfoSuccecc:(PLVBeautyResourceManager *)beautyResourceManager {
    if (self.beautyResourceManager.resourceIsReady) {
        [self setupBeauty];
    } else {
        [self.beautyResourceManager asyncGetResource];
    }
}

- (void)plvBeautyResourceManagerdidGetResourceSuccecc:(PLVBeautyResourceManager *)beautyResourceManager {
    plv_dispatch_main_async_safe(^{
        [self setupBeauty];
    })
}

- (void)plvBeautyResourceManager:(PLVBeautyResourceManager *)beautyResourceManager didGetResourceFail:(NSError *)error {
    [self callbackForBeautyProcessDidOccurError:error];
}

@end
