//
//  PLVLiveVideoAPI.m
//  PLVLiveScenesSDK
//
//  Created by zykhbl on 2018/7/27.
//  Copyright © 2018年 PLV. All rights reserved.
//

#import "PLVLiveVideoAPI.h"
#import <PLVFoundationSDK/PLVFoundationSDK.h>
#import "PLVLiveVideoConfig.h"
#import "PLVImageUpload.h"
#import "PLVLiveAPIUtils.h"
#import "PLVWErrorManager.h"
#import "PLVWELogEventDefine.h"
#import "PLVWLogReporterManager.h"
#import "PLVLivePrivateAPI.h"
#import "PLVLiveVClassAPI.h"
#import "PLVLiveHttpDnsManager.h"
#import "PLVLiveVideoConfig+PrivateInfo.h"
#import "PLVSocketManager+Private.h"

NSString *const PLVApichatDomain = @"https://apichat.polyv.net/";

static NSString *private_apichat_domain = PLVApichatDomain;

static NSString *kLoginVerifyAPI = @"https://api.polyv.net/live/inner/v3/sdk/verify";

static NSInteger _requestCount;

static NSTimeInterval _lastRequestTime;

static NSLock *_kLoginLock;

@implementation PLVLiveVideoAPI

+ (void)initialize {
    if (self == [PLVLiveVideoAPI self]) {
        _requestCount = 0;
        _lastRequestTime = 0;
        _kLoginLock = [[NSLock alloc] init];
    }
}

+ (void)setPrivateApichatDomainName:(NSString *)domainName {
    if (domainName.length > 0) {
        private_apichat_domain = [NSString stringWithFormat:@"https://%@/", domainName];
    } else {
        private_apichat_domain = PLVApichatDomain;
    }
}

#pragma mark - 登录校验

+ (void)verifyPermissionWithChannelId:(NSUInteger)channelId vid:(NSString *)vid appId:(NSString *)appId userId:(NSString *)userId appSecret:(NSString *)appSecret completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    [self verifyPermissionWithChannelId:channelId vid:vid appId:appId userId:userId completion:completion failure:failure];
}

+ (void)verifyPermissionWithChannelId:(NSUInteger)channelId vid:(NSString *)vid appId:(NSString *)appId userId:(NSString *)userId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    if (appId.length == 0) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId不能为空!")];
        return;
    }
    
    if (![self isRequestAllowed]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeLoginFail desc:PLVFDLocalizableString(@"登录请求过于频繁，请稍后再试")];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:appId forKey:@"appId"];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    [params setObject:timestamp forKey:@"timestamp"];
    if (userId.length > 0) {
        [params setObject:userId forKey:@"userId"];
    }
    if(channelId != 0) {
        [params setObject:@(channelId) forKey:@"channelId"];
    }
    if (vid.length) {
        [params setObject:vid forKey:@"vid"];
    }
    
    NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@%@", key, params[key]];
    }
    NSString *plain = [NSString stringWithFormat:@"polyv_sdk_api_innor%@polyv_sdk_api_innor", paramStr];
    NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    [params setObject:sign forKey:@"sign"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:kLoginVerifyAPI params:params httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 && [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            completion(responseDict[@"data"]);
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:failure networkError:error];
    }];
}

+ (void)verifyLivePermissionWithChannelId:(NSUInteger)channelId
                                   userId:(NSString *)userId
                                    appId:(NSString *)appId
                                appSecret:(NSString *)appSecret
                               completion:(void (^)(NSDictionary *))completion
                                  failure:(void (^)(NSError *))failure {
    [self verifyLivePermissionWithChannelId:channelId userId:userId appId:appId completion:completion failure:failure];
}

+ (void)verifyLivePermissionWithChannelId:(NSUInteger)channelId
                                   userId:(NSString *)userId
                                    appId:(NSString *)appId
                               completion:(void (^)(NSDictionary *))completion
                                  failure:(void (^)(NSError *))failure {
    if (channelId == 0 ||
        ![PLVFdUtil checkStringUseable:appId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeLiveLogin_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道号与appId不可为空")];
        return;
    }
    
    if (![self isRequestAllowed]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeLoginFail desc:PLVFDLocalizableString(@"登录请求过于频繁，请稍后再试")];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:@(channelId) forKey:@"channelId"];
    [params setObject:appId forKey:@"appId"];
    if ([PLVFdUtil checkStringUseable:userId]) {
        [params setObject:userId forKey:@"userId"];
    }
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:kLoginVerifyAPI params:params SHA256:YES signatureNonce:YES encrypt:YES forceMode:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 &&
            [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled = PLV_SafeBoolForDictKey(responseDict[@"data"], @"sdkSafetyEnabled");
            completion(responseDict[@"data"]);
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveLogin_CodeError
                               info:responseDict[@"data"]
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveLogin_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)verifyVodPermissionWithChannelId:(NSUInteger)channelId
                                     vid:(NSString *)vid
                                  userId:(NSString *)userId
                                   appId:(NSString *)appId
                               appSecret:(NSString *)appSecret
                              completion:(void (^)(NSDictionary *data))completion
                                 failure:(void (^)(NSError *error))failure {
    [self verifyVodPermissionWithChannelId:channelId vid:vid userId:userId appId:appId completion:completion failure:failure];
}

+ (void)verifyVodPermissionWithChannelId:(NSUInteger)channelId
                                     vid:(NSString *)vid
                                  userId:(NSString *)userId
                                   appId:(NSString *)appId
                              completion:(void (^)(NSDictionary *data))completion
                                 failure:(void (^)(NSError *error))failure {
    if (channelId == 0 ||![PLVFdUtil checkStringUseable:vid] ||
        ![PLVFdUtil checkStringUseable:appId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeVodLogin_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道号、vid与appId都不可为空")];
        return;
    }
    
    if (![self isRequestAllowed]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeLoginFail desc:PLVFDLocalizableString(@"登录请求过于频繁，请稍后再试")];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:vid forKey:@"vid"];
    [params setObject:appId forKey:@"appId"];
    if ([PLVFdUtil checkStringUseable:userId]) {
        [params setObject:userId forKey:@"userId"];
    }
    if(channelId != 0) {
        [params setObject:@(channelId) forKey:@"channelId"];
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:kLoginVerifyAPI params:params SHA256:YES signatureNonce:YES encrypt:YES forceMode:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 &&
            [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled = PLV_SafeBoolForDictKey(responseDict[@"data"], @"sdkSafetyEnabled");
            completion(responseDict[@"data"]);
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodLogin_CodeError
                               info:responseDict[@"data"]
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodLogin_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)loadPushInfoWithChannelId:(NSString *)channelId
                         password:(NSString *)password
                      channelType:(PLVChannelType)channelType
                       completion:(void (^)(NSDictionary *data, NSString *rtmpUrl))completion
                          failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:password]) {
        NSDictionary *errorInformation = @{@"channelId":(channelId ?: @""),
                                           @"password":(password ?: @"")};
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulLink
                           code:PLVFLinkErrorCodeTeacherLoginFailed_ParameterError
                           info:errorInformation
               errorDescription:PLVFDLocalizableString(@"频道号与密码不可为空")];
        return;
    }
    
    if ((channelType & PLVChannelTypePPT) == 0 &&
        (channelType & PLVChannelTypeAlone) == 0) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulLink
                           code:PLVFLinkErrorCodeTeacherLoginFailed_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道类型不支持")];
        return;
    }
    
    if (![self isRequestAllowed]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeLoginFail desc:PLVFDLocalizableString(@"登录请求过于频繁，请稍后再试")];
        return;
    }
    
    NSString *password_md5 = [PLVDataUtil md5HexDigest:password];
    NSString *url = @"https://api.polyv.net/live/inner/v3/sdk/teacher-login";
    NSString *version = [NSString stringWithFormat:@"%@-%@", [PLVLiveVideoConfig sharedInstance].playerName, [PLVLiveVideoConfig sharedInstance].playerVersion];
    NSDictionary *params = @{@"channelId" : channelId,
                             @"passwd" : password_md5,
                             @"version" : version};
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES forceMode:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
        } httpMethod:PLV_HM_POST];
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 && [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
            NSString *stream = PLV_SafeStringForDictKey(data, @"stream");
            if (stream.length > 0) { //登录成功
                NSString *channelId = PLV_SafeStringForDictKey(data, @"channelId");
                NSString *userId = PLV_SafeStringForDictKey(data, @"useId");
                NSString *appId = PLV_SafeStringForDictKey(data, @"appId");
                NSString *appSecret = PLV_SafeStringForDictKey(data, @"appSecret");
                if (channelId == nil || channelId.length == 0) {
                    NSString *errorMessage = PLVFDLocalizableString(@"频道号不可为空");
                    [self callBackFailBlock:failure
                                      modul:PLVFErrorCodeModulLink
                                       code:PLVFLinkErrorCodeTeacherLoginFailed_DataError
                                       info:responseDict
                           errorDescription:errorMessage];
                } else if (userId == nil || userId.length == 0 ||
                           appId == nil || appId.length == 0 ||
                           appSecret == nil || appSecret.length == 0) {
                    NSString *errorMessage = PLVFDLocalizableString(@"账号配置参数不可为空");
                    [self callBackFailBlock:failure
                                      modul:PLVFErrorCodeModulLink
                                       code:PLVFLinkErrorCodeTeacherLoginFailed_DataError
                                       info:responseDict
                           errorDescription:errorMessage];
                } else { // 获取推流地址
                    /// 讲师登录 RTC 类型限制
                    NSString *liveScene = PLV_SafeStringForDictKey(data, @"liveScene");
                    PLVChannelType apiChannelType = PLVChannelTypeUnknown;
                    if ([liveScene isEqualToString:@"ppt"]) {
                        apiChannelType = PLVChannelTypePPT;
                    } else if ([liveScene isEqualToString:@"alone"]) {
                        apiChannelType = PLVChannelTypeAlone;
                    }
                    NSString *rtcType = PLV_SafeStringForDictKey(data, @"rtcType");
                    NSDictionary *clientParams = PLV_SafeDictionaryForDictKey(data, @"clientParams");
                    [PLVLiveVideoConfig sharedInstance].cp = clientParams;
                    [PLVLiveVideoConfig sharedInstance].cpstEnabled = PLV_SafeBoolForDictKey(data, @"clientPushStreamTemplateEnabled");
                    [PLVLiveVideoConfig sharedInstance].pstJson = PLV_SafeDictionaryForDictKey(data, @"pushStreamTemplateJson");
                    [PLVLiveVideoConfig sharedInstance].sdkSafetyEnabled = PLV_SafeBoolForDictKey(data, @"sdkSafetyEnabled");
                    // 解析美颜类型
                    NSString *beautyType = PLV_SafeStringForDictKey(data, @"appBeautyType");
                    [PLVLiveVideoConfig sharedInstance].beautyType = beautyType;
                    
                    if ([PLVFdUtil checkStringUseable:rtcType]) {
                        BOOL rtcTypeSupport = NO;
                        if (apiChannelType == PLVChannelTypePPT) {
                            rtcTypeSupport = ([rtcType isEqualToString:@"agora"] || [rtcType isEqualToString:@"urtc"] || [rtcType isEqualToString:@"trtc"] || [rtcType isEqualToString:@"volc"]);
                        } else if (apiChannelType == PLVChannelTypeAlone){
                            rtcTypeSupport = ([rtcType isEqualToString:@"agora"] || [rtcType isEqualToString:@"urtc"] || [rtcType isEqualToString:@"trtc"] || [rtcType isEqualToString:@"volc"]);
                        }
                        if (!rtcTypeSupport) {
                            [self callBackFailBlock:failure
                                              modul:PLVFErrorCodeModulLink
                                               code:PLVFLinkErrorCodeTeacherLoginFailed_ParameterError
                                               info:nil
                                   errorDescription:PLVFDLocalizableString(@"频道RTC类型暂不支持")];
                            return;
                        }
                    } else {
                        [self callBackFailBlock:failure
                                          modul:PLVFErrorCodeModulLink
                                           code:PLVFLinkErrorCodeTeacherLoginFailed_ParameterError
                                           info:nil
                               errorDescription:PLVFDLocalizableString(@"频道RTC类型无效")];
                        return;
                    }
                    [PLVLivePrivateAPI getRtmpUrlWithTeacherLoginResponse:data completion:^(NSString *rtmpUrl) {
                        if (completion) {
                            completion(data, rtmpUrl);
                        }
                    }];
                }
            } else { //登录失败
                NSString *errorMessage = data[@"msg"] ?: @"";
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulLink
                                   code:PLVFLinkErrorCodeTeacherLoginFailed_DataError
                                   info:responseDict
                       errorDescription:errorMessage];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
       
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulLink
                               code:PLVFLinkErrorCodeTeacherLoginFailed_DataError
                               info:error.localizedDescription];
        } else {
            NSString *errorDes = error.userInfo[NSLocalizedDescriptionKey];
            NSArray *errorArr = [errorDes componentsSeparatedByString:@","];
            NSString *message = errorArr.firstObject;
            if ([PLVFdUtil checkStringUseable:message] && [message hasPrefix:@"message:"] ) {
                errorDes = [message substringFromIndex:8];
                [self callBackFailBlock:failure networkError:PLVErrorCreate(error.domain, error.code, errorDes)];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }
    }];
}

#pragma mark - 获取频道类型 & 直播状态

+ (void)liveStatus:(NSString *)channelId completion:(void (^)(BOOL, NSString *))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeLiveInfo_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道号不可为空")];
        return;
    }
    
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channels/%@/live-status", channelId];//直播状态接口：使用 channelId 参数
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 && [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            NSString *data = responseDict[@"data"] ?: @"";
            NSArray<NSString *> *splitStrs = [data componentsSeparatedByString:@","];
            if (![PLVFdUtil checkStringUseable:data] ||
                [splitStrs count] < 2) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInit
                                   code:PLVFInitErrorCodeLiveInfo_DataError
                                   info:responseDict
                       errorDescription:PLVFDLocalizableString(@"接口返回数据解析出错")];
            } else {
                if (completion) {
                    BOOL liveing = splitStrs[0].boolValue;
                    NSString *liveType = splitStrs[1];
                    completion(liveing, liveType);
                }
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveInfo_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveInfo_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)liveStatus2:(NSString *)channelId completion:(void (^)(PLVChannelType, PLVChannelLiveStreamState))completion failure:(void (^)(NSError * _Nonnull))failure{
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channels/%@/live-status2", channelId];//直播状态接口：使用 channelId 参数
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 && [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            if (completion) {
                NSString *data = responseDict[@"data"];
                NSArray<NSString *> *splitStrs = [data componentsSeparatedByString:@","];
                NSString *liveState = splitStrs[0];
                PLVChannelLiveStreamState streamState = PLVChannelLiveStreamState_Unknown;
                if ([liveState isEqualToString:@"live"]) {
                    streamState = PLVChannelLiveStreamState_Live;
                } else if ([liveState isEqualToString:@"end"]) {
                    streamState = PLVChannelLiveStreamState_End;
                } else if ([liveState isEqualToString:@"stop"]) {
                    streamState = PLVChannelLiveStreamState_Stop;
                }
                
                NSString *liveType = splitStrs[1];
                PLVChannelType channelType = PLVChannelTypeUnknown;
                if ([liveType isEqualToString:@"ppt"]) {
                    channelType = PLVChannelTypePPT;
                } else if ([liveType isEqualToString:@"alone"]) {
                    channelType = PLVChannelTypeAlone;
                } else if ([liveState isEqualToString:@"seminar"]) {
                    channelType = PLVChannelTypeSeminar;
                }
                completion(channelType, streamState);
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
    } fail:failure];
}

+ (void)liveStatus2:(NSString *)channelId appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(PLVChannelType, PLVChannelLiveStreamState))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeLiveInfo_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道号、appId、appSecret均不可为空")];
        return;
    }
    
    NSString *url = @"https://api.polyv.net/live/v3/channel/live-status2";//直播状态接口
    NSDictionary *param = @{
        @"channelId" : channelId,
        @"appId" : appId
    };
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:param SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200 && [@"success" isEqualToString:(NSString *)responseDict[@"status"]]) {
            if (completion) {
                NSString *data = responseDict[@"data"];
                NSArray<NSString *> *splitStrs = [data componentsSeparatedByString:@","];
                NSString *liveState = splitStrs[0];
                PLVChannelLiveStreamState streamState = PLVChannelLiveStreamState_Unknown;
                if ([liveState isEqualToString:@"live"]) {
                    streamState = PLVChannelLiveStreamState_Live;
                } else if ([liveState isEqualToString:@"end"]) {
                    streamState = PLVChannelLiveStreamState_End;
                } else if ([liveState isEqualToString:@"stop"]) {
                    streamState = PLVChannelLiveStreamState_Stop;
                }
                
                NSString *liveType = (splitStrs.count > 1) ? splitStrs[1] : nil;
                PLVChannelType channelType = PLVChannelTypeUnknown;
                if ([liveType isEqualToString:@"ppt"]) {
                    channelType = PLVChannelTypePPT;
                } else if ([liveType isEqualToString:@"alone"]) {
                    channelType = PLVChannelTypeAlone;
                } else if ([liveState isEqualToString:@"seminar"]) {
                    channelType = PLVChannelTypeSeminar;
                }
                completion(channelType, streamState);
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveInfo_ParameterError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeLiveInfo_ParameterError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)getVodType:(NSString *)vodId completion:(void (^)(PLVChannelType))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:vodId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeVodInfo_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"vid不可为空")];
        return;
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/inner/v3/sdk/playback/get-type" params:@{@"vid" : vodId} SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *data = responseDict[@"data"] ?: @{};
            id liveTypeObject = data[@"liveType"];
            if (![PLVFdUtil checkDictionaryUseable:data] ||
                !liveTypeObject) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInit
                                   code:PLVFInitErrorCodeVodInfo_DataError
                                   info:responseDict
                       errorDescription:PLVFDLocalizableString(@"接口返回数据解析出错")];
            } else {
                if (completion) {
                    BOOL liveType = ((NSNumber *)liveTypeObject).boolValue;
                    completion(liveType ? PLVChannelTypePPT : PLVChannelTypeAlone);
                }
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodInfo_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodInfo_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)getLiveRecordTypeWithChannelId:(NSString *)channelId fileId:(NSString *)fileId appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(PLVChannelType))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:fileId] || ![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeVodInfo_ParameterError
                           info:nil
               errorDescription:PLVFDLocalizableString(@"频道号、fileId、appId、appSecret均不可为空")];
        return;
    }
    NSDictionary *paramDict = @{
        @"fileId" : fileId,
        @"channelId" : channelId,
        @"appId" : appId
    };
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/record/get" params:paramDict plainBlock:^NSString *(NSString *timestamp) {
        return [NSString stringWithFormat:@"%@appId%@channelId%@fileId%@timestamp%@%@", appSecret, appId, channelId, fileId, timestamp, appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSString *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *data = responseDict[@"data"] ?: @{};
            id liveTypeObject = data[@"liveType"];
            if (![PLVFdUtil checkDictionaryUseable:data] ||
                !liveTypeObject) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInit
                                   code:PLVFInitErrorCodeVodInfo_DataError
                                   info:responseDict
                       errorDescription:PLVFDLocalizableString(@"接口返回数据解析出错")];
            } else {
                if (completion) {
                    BOOL liveType = [(NSString *)liveTypeObject isEqualToString:@"ppt"] ;
                    completion(liveType ? PLVChannelTypePPT : PLVChannelTypeAlone);
                }
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodInfo_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeVodInfo_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)getChannelMenuInfos:(NSString *)channelId completion:(void(^)(PLVLiveVideoChannelMenuInfo *channelMenuInfo))completion failure:(void (^)(NSError *))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeChannelInfo_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:channelId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"channelId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeChannelInfo_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSDictionary *paramDict = @{
        @"channelId" : channelId,
        @"appId" : appId
    };
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/applet/sdk/get-channel-detail" params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];//获取频道菜单列表
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if ([responseDict[@"status"] isEqualToString:@"success"]) {
            NSDictionary *data = responseDict[@"data"];
            PLVLiveVideoChannelMenuInfo *menuInfo = [[PLVLiveVideoChannelMenuInfo alloc] initWithDictionary:data];
            if (![PLVFdUtil checkDictionaryUseable:data] ||
                menuInfo == nil) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInit
                                   code:PLVFInitErrorCodeChannelInfo_DataError
                                   info:responseDict
                       errorDescription:PLVFDLocalizableString(@"接口返回数据解析出错")];
            } else {
                NSString *httpDnsKey = data[@"httpDnsKey"];
                if ([PLVFdUtil checkStringUseable:httpDnsKey]) {
                    [[PLVLiveHttpDnsManager sharedManager] setupHttpDNSWithSecretKey:httpDnsKey];
                }
                [PLVLiveVideoConfig sharedInstance].appPushStreamParams = PLV_SafeStringForDictKey(data, @"appPushStreamParams");
                if (completion) {
                    completion(menuInfo);
                }
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeChannelInfo_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeChannelInfo_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)updateChannelName:(NSString *)channelName channelId:(NSString *)channelId completion:(void (^)(void))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelName] ||
        ![PLVFdUtil checkStringUseable:channelId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"channelName或channelId不可为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInit
                           code:PLVFInitErrorCodeUpdateChannelName_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSMutableDictionary *mParam = [NSMutableDictionary dictionary];
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    mParam[@"appId"] = liveConfig.appId;
    mParam[@"channelId"] = channelId;
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    mParam[@"timestamp"] = timestamp;
    NSString *urlStr = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/basic/update?channelId=%@&appId=%@&timestamp=%@", channelId, liveConfig.appId, timestamp];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        mParam[@"signatureNonce"] = signatureNonceString;
        urlStr = [NSString stringWithFormat:@"%@&signatureNonce=%@", urlStr, signatureNonceString];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt) {
        mParam[@"encryptResponseType"] = @(1);
        urlStr = [NSString stringWithFormat:@"%@&encryptResponseType=1", urlStr];
    }
    NSString *sign;
    NSString *plain;
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        mParam[@"signatureMethod"] = @"SHA256";
        urlStr = [NSString stringWithFormat:@"%@&signatureMethod=SHA256", urlStr];
        plain = [PLVLiveAPIUtils createStringWithParamDict:mParam];
        sign = [[PLVDataUtil sha256String:plain] uppercaseString];
    } else {
        plain = [PLVLiveAPIUtils createStringWithParamDict:mParam];
        sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    }
    urlStr = [NSString stringWithFormat:@"%@&sign=%@", urlStr ,sign];
    
    NSString *basicSetting = [NSString stringWithFormat:@"{\"basicSetting\":{\"name\":\"%@\"}}", channelName];
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt) {
        basicSetting = [[PLVDataUtil AES256EncryptData:[basicSetting dataUsingEncoding:NSUTF8StringEncoding] withKey:liveConfig.appSecret iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
        basicSetting = [basicSetting stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLUserAllowedCharacterSet]];
    }
    
    NSData *jsonData = [basicSetting dataUsingEncoding:NSUTF8StringEncoding];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlStr httpMethod:PLV_HM_POST];
    [request setHTTPBody:jsonData];
    [request setValue:@"application/json;charset=utf8" forHTTPHeaderField:@"Content-Type"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt) {
        [request setValue:@"1" forHTTPHeaderField:@"x-e-type"];
    }

    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if ([responseDict[@"status"] isEqualToString:@"success"]) {
            if (completion) {
                completion();
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulInit code:PLVFInitErrorCodeUpdateChannelName_CodeError info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInit
                               code:PLVFInitErrorCodeUpdateChannelName_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}


#pragma mark - 文档

+ (void)requestDocumentListWithChannelId:(NSString *)channelId completion:(void (^)(NSArray<NSDictionary *> *responseArray))completion failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeDocumentList_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    if(![PLVFdUtil checkStringUseable:channelId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数channelId为空");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeDocumentList_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:channelId forKey:@"channelId"];
    NSNumber *limitNumber = @(9999);
    [params setObject:limitNumber forKey:@"limit"];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    [params setObject:timestamp forKey:@"timestamp"];
    [params setObject:appId forKey:@"appId"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        [params setObject:@"SHA256" forKey:@"signatureMethod"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        [params setObject:signatureNonceString forKey:@"signatureNonce"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_AES) {
        [params setObject:@(1) forKey:@"encryptResponseType"];
    }
    
    NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@%@", key, params[key]];
    }
    NSString *plain = [NSString stringWithFormat:@"%@%@%@", appSecret, paramStr, appSecret];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        NSString *sign = [[PLVDataUtil sha256String:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    } else {
        NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    }
    
    [self requestDocumentListWithParams:params completion:completion failure:failure];
}


+ (void)requestDocumentListWithLessonId:(NSString *)lessonId
                              completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion
                                failure:(void (^)(NSError * _Nonnull))failure {
    [self requestDocumentListWithLessonId:lessonId courseCode:nil teacher:YES completion:completion failure:failure];
}

+ (void)requestDocumentListWithLessonId:(NSString *)lessonId courseCode:(NSString *)courseCode teacher:(BOOL)teacher completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion failure:(void (^)(NSError * _Nonnull))failure {

    if(![PLVFdUtil checkStringUseable:lessonId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数lessonId为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeDocumentList_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    if (teacher) {
        [PLVLiveVClassAPI teacherGetLiveAPIChannelTokenWithLessonId:lessonId
                                                   success:^(NSDictionary * _Nonnull responseDict) {
            [self dealRequestDocumentListSuccessWithResponseDict:responseDict completion:completion failure:failure];
        } failure:^(NSError * _Nonnull error) {
            [self dealRequestDocumentListFailureWithError:error failure:failure];
        }];
    } else {
        [PLVLiveVClassAPI watcherGetLiveAPIChannelTokenWithLessonId:lessonId courseCode:courseCode
                                                   success:^(NSDictionary * _Nonnull responseDict) {
            [self dealRequestDocumentListSuccessWithResponseDict:responseDict completion:completion failure:failure];
        } failure:^(NSError * _Nonnull error) {
            [self dealRequestDocumentListFailureWithError:error failure:failure];
        }];
    }
}

+ (void)requestDocumentListWithParams:(NSDictionary *)params
                           completion:(void (^)(NSArray<NSDictionary *> *responseArray))completion
                              failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkDictionaryUseable:params]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数params为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeDocumentList_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *requestUrl = @"https://api.polyv.net/live/v3/channel/document/doc-list";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:requestUrl
                                                            params:params
                                                        httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
            NSDictionary *dic = PLV_SafeDictionaryForDictKey(responseDict, @"data");
            NSArray *documentArray = PLV_SafeArraryForDictKey(dic, @"contents");
            if (documentArray &&
                [documentArray isKindOfClass:[NSArray class]]) {
                if (completion) {
                    completion(documentArray);
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPPT
                                   code:PLVFPPTErrorCodeDocumentList_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeDocumentList_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeDocumentList_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)deleteDocumentWithChannelId:(NSString *)channelId fileId:(NSString *)fileId completion:(void (^)(void))completion failure:(void (^)(NSError *error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:fileId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数channelId或fileId为空");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeDocumentDelete_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    NSString *plain = [NSString stringWithFormat:@"%@%@livepolyv", channelId, fileId];
    NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:sign forKey:@"sign"];
    
    NSString *urlString = [NSString stringWithFormat:@"https://document-2.polyv.net/api/channel/%@/file/%@", channelId, fileId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlString params:params httpMethod:PLV_HM_DELETE];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
            if (completion) {
                completion();
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeDocumentDelete_CodeError info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeDocumentDelete_DataError info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)getDocumentConvertStatusWithChannelId:(NSString *)channelId fileId:(NSString *)fileId completion:(void (^)(NSArray <NSDictionary *> *responseArray))completion failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeStatusGet_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:fileId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数channelId或fileId为空");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulPPT code:PLVFPPTErrorCodeStatusGet_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:appId forKey:@"appId"];
    [params setObject:channelId forKey:@"channelId"];
    [params setObject:fileId forKey:@"fileId"];
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    [params setObject:timestamp forKey:@"timestamp"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        [params setObject:@"SHA256" forKey:@"signatureMethod"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        [params setObject:signatureNonceString forKey:@"signatureNonce"];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt &&
        [PLVFSignConfig sharedInstance].encryptType == PLVEncryptType_AES) {
        [params setObject:@(1) forKey:@"encryptResponseType"];
    }
    
    NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
        return [obj1 compare:obj2 options:NSCaseInsensitiveSearch];
    }];
    NSMutableString *paramStr = [NSMutableString string];
    for (NSString *key in keys) {
        [paramStr appendFormat:@"%@%@", key, params[key]];
    }
    NSString *plain = [NSString stringWithFormat:@"%@%@%@", appSecret, paramStr, appSecret];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        NSString *sign = [[PLVDataUtil sha256String:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    } else {
        NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
        [params setObject:sign forKey:@"sign"];
    }
    
    [self getDocumentConvertStatusWithParams:params completion:completion failure:failure];
}

+ (void)getDocumentConvertStatusWithLessonId:(NSString *)lessonId
                                      fileId:(NSString *)fileId
                                  completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion
                                     failure:(void (^)(NSError * _Nonnull))failure {
    [self getDocumentConvertStatusWithLessonId:lessonId fileId:fileId courseCode:nil teacher:YES completion:completion failure:failure];
}

+ (void)getDocumentConvertStatusWithLessonId:(NSString *)lessonId fileId:(NSString *)fileId courseCode:(NSString *)courseCode teacher:(BOOL)teacher completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion failure:(void (^)(NSError * _Nonnull))failure {
    
    if(![PLVFdUtil checkStringUseable:lessonId] ||
       ![PLVFdUtil checkStringUseable:fileId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数lessonId或fileId为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeDocumentList_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    if (teacher) {
        [PLVLiveVClassAPI teacherGetLiveAPIChannelTokenWithLessonId:lessonId success:^(NSDictionary * _Nonnull responseDict) {
            [self dealDocumentConvertStatusGetTokenSuccessWithCompletion:completion failure:failure fileId:fileId responseDict:responseDict];
        } failure:^(NSError * _Nonnull error) {
            [self dealDocumentConvertStatusGetTokenFailureWithError:error failure:failure];
        }];
    } else {
        [PLVLiveVClassAPI watcherGetLiveAPIChannelTokenWithLessonId:lessonId courseCode:courseCode success:^(NSDictionary * _Nonnull responseDict) {
            [self dealDocumentConvertStatusGetTokenSuccessWithCompletion:completion failure:failure fileId:fileId responseDict:responseDict];
        } failure:^(NSError * _Nonnull error) {
            [self dealDocumentConvertStatusGetTokenFailureWithError:error failure:failure];
        }];
    }
}

+ (void)getDocumentConvertStatusWithParams:(NSDictionary *)params
                                completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion
                                   failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkDictionaryUseable:params]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数params为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeStatusGet_CodeError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *requestUrl = @"https://api.polyv.net/live/v3/channel/document/status/get";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:requestUrl params:params httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
            NSArray *data = PLV_SafeArraryForDictKey(responseDict, @"data");
            if (data &&
                [data isKindOfClass:[NSArray class]]) {
                if (completion) {
                    completion(data);
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulPPT
                                   code:PLVFPPTErrorCodeStatusGet_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeStatusGet_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeStatusGet_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark  处理文档请求回调

+ (void)dealRequestDocumentListSuccessWithResponseDict:(NSDictionary * _Nonnull)responseDict
                                            completion:(void (^)(NSArray<NSDictionary *> * _Nonnull))completion
                                               failure:(void (^)(NSError * _Nonnull))failure{
    if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
        NSString *token = PLV_SafeStringForDictKey(responseDict, @"token");
        NSString *appId = PLV_SafeStringForDictKey(responseDict, @"appId");
        NSString *channelId = PLV_SafeStringForDictKey(responseDict, @"channelId");
        
        if (![PLVFdUtil checkStringUseable:token] ||
            ![PLVFdUtil checkStringUseable:appId] ||
            ![PLVFdUtil checkStringUseable:channelId]) {
            NSString *errorMessage = PLVFDLocalizableString(@"参数token或appId或channelId为空");
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeDocumentList_ParameterError
                               info:errorMessage
                   errorDescription:errorMessage];
            return;
        }
        
        NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
        [params setObject:channelId forKey:@"channelId"];
        NSNumber *limitNumber = @(9999);
        [params setObject:limitNumber forKey:@"limit"];
        NSString *timestamp = [PLVFdUtil curTimeStamp];
        [params setObject:timestamp forKey:@"timestamp"];
        [params setObject:appId forKey:@"appId"];
        [params setObject:token forKey:@"channelToken"];
        
        [self requestDocumentListWithParams:params completion:completion failure:failure];
        
    } else {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeStatusGet_CodeError
                           info:responseDict];
    }
}

+ (void)dealRequestDocumentListFailureWithError:(NSError * _Nonnull)error
                                        failure:(void (^)(NSError * _Nonnull))failure{
    if (error.code == PLVNetworkErrorCodeDecodeFail) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeDocumentList_DataError
                           info:error.localizedDescription];
    } else {
        [self callBackFailBlock:failure networkError:error];
    }
}

+ (void)dealDocumentConvertStatusGetTokenSuccessWithCompletion:(void (^ _Nonnull)(NSArray<NSDictionary *> * _Nonnull))completion failure:(void (^ _Nonnull)(NSError * _Nonnull))failure fileId:(NSString * _Nonnull)fileId responseDict:(NSDictionary * _Nonnull)responseDict {
    if ([PLVFdUtil checkDictionaryUseable:responseDict]) {
        NSString *token = PLV_SafeStringForDictKey(responseDict, @"token");
        NSString *appId = PLV_SafeStringForDictKey(responseDict, @"appId");
        NSString *channelId = PLV_SafeStringForDictKey(responseDict, @"channelId");
        
        if (![PLVFdUtil checkStringUseable:token] ||
            ![PLVFdUtil checkStringUseable:appId] ||
            ![PLVFdUtil checkStringUseable:channelId]) {
            NSString *errorMessage = PLVFDLocalizableString(@"参数token或appId或channelId为空");
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulPPT
                               code:PLVFPPTErrorCodeDocumentList_ParameterError
                               info:errorMessage
                   errorDescription:errorMessage];
            return;
        }
        
        NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
        [params setObject:channelId forKey:@"channelId"];
        NSString *timestamp = [PLVFdUtil curTimeStamp];
        [params setObject:timestamp forKey:@"timestamp"];
        [params setObject:fileId forKey:@"fileId"];
        [params setObject:appId forKey:@"appId"];
        [params setObject:token forKey:@"channelToken"];
        
        [self getDocumentConvertStatusWithParams:params completion:completion failure:failure];
        
    } else {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeStatusGet_CodeError
                           info:responseDict];
    }
}

+ (void)dealDocumentConvertStatusGetTokenFailureWithError:(NSError * _Nonnull)error failure:(void (^ _Nonnull)(NSError * _Nonnull))failure {
    if (error.code == PLVNetworkErrorCodeDecodeFail) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulPPT
                           code:PLVFPPTErrorCodeDocumentList_DataError
                           info:error.localizedDescription];
    } else {
        [self callBackFailBlock:failure networkError:error];
    }
}

#pragma mark - 聊天室相关

+ (void)getChatTokenWithChannelId:(NSString *)channelId
                           userId:(NSString *)userId
                       completion:(void (^)(NSDictionary *))completion
                          failure:(void (^)(NSError *error))failure {
    [self getChatTokenWithChannelId:channelId userId:userId role:@"viewer" completion:completion failure:failure];
}

+ (void)getChatTokenWithChannelId:(NSString *)channelId
                           userId:(NSString *)userId
                             role:(NSString *)role
                       completion:(void (^)(NSDictionary *))completion
                          failure:(void (^)(NSError *error))failure {
    [self getChatTokenWithChannelId:channelId userId:userId role:role groupId:nil completion:completion failure:failure];
}

+ (void)getChatTokenWithChannelId:(NSString *)channelId
                           userId:(NSString *)userId
                             role:(NSString *)role
                          groupId:(NSString *)groupId
                       completion:(void (^)(NSDictionary *))completion
                          failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulSocket
                           code:PLVFSocketErrorCodeGetToken_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"channelId或userId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulSocket
                           code:PLVFSocketErrorCodeGetToken_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    if (![PLVFdUtil checkStringUseable:role]) {
        NSString *errorMessage = PLVFDLocalizableString(@"role无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulSocket
                           code:PLVFSocketErrorCodeGetToken_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *origin = @"iOS-SDK";
    NSString * urlEncodeUserId = [PLVFdUtil URLEncodedString:userId]; /// 注意：此处userId是 聊天室用户ID，非直播账号用户ID
    NSString *groupEncodeId = [self URLEncodedString:groupId];
    NSString *groupUnEncodeId = [PLVFdUtil checkStringUseable:groupId] ? groupId : @"";
    BOOL isSpecialRole = [role isEqualToString:@"teacher"] || [role isEqualToString:@"guest"];
    // 特殊身份讲师嘉宾不响应分组Id
    NSDictionary *paramDict;
    if (!isSpecialRole) {
        paramDict = @{
            @"channelId" : channelId,
            @"appId" : appId,
            @"role" : role,
            @"userId" : urlEncodeUserId,
            @"origin" : origin,
            @"groupId" : groupEncodeId
        };
    } else {
        paramDict = @{
            @"channelId" : channelId,
            @"appId" : appId,
            @"role" : role,
            @"userId" : urlEncodeUserId,
            @"origin" : origin
        };
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/common/get-chat-token"
                                                            params:paramDict
                                                            SHA256:YES
                                                    signatureNonce:YES
                                                           encrypt:YES
                                                    requestEncrypt:YES
                                                        plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        NSMutableDictionary *unEncodeParamDict = [NSMutableDictionary dictionaryWithDictionary:paramDict];
        [unEncodeParamDict setValue:userId forKey:@"userId"];
        if (!isSpecialRole) {
            [unEncodeParamDict setValue:groupUnEncodeId forKey:@"groupId"];
        }
        return [PLVLiveAPIUtils createStringWithParamDict:unEncodeParamDict];
    } httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict && [responseDict isKindOfClass:[NSDictionary class]]) {
            if ([responseDict[@"status"] isEqualToString:@"success"]) {
                NSDictionary *data = responseDict[@"data"];
                if ([data isKindOfClass:NSDictionary.class]) {
                    completion(data);
                } else {
                    [self callBackFailBlock:failure
                                      modul:PLVFErrorCodeModulSocket
                                       code:PLVFSocketErrorCodeGetToken_DataError
                                       info:responseDict
                           errorDescription:PLVFDLocalizableString(@"返回数据格式异常")];
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulSocket
                                   code:PLVFSocketErrorCodeGetToken_CodeError
                                   info:responseDict
                       errorDescription:responseDict[@"message"]];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulSocket
                               code:PLVFSocketErrorCodeGetToken_DataError
                               info:responseDict
                   errorDescription:PLVFDLocalizableString(@"返回数据格式异常")];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulSocket
                               code:PLVFSocketErrorCodeGetToken_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)loadChatroomFunctionSwitchWithRoomId:(NSUInteger)roomId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    if (roomId <= 0) {
        NSString *errorMessage = PLVFDLocalizableString(@"roomId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeGetSwitch_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *url = @"https://api.polyv.net/live/inner/v3/channel/switch/get";
    NSDictionary *dict = @{@"channelId" : @(roomId)};
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:dict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_switch_api_innor"];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSInteger code = [responseDict[@"code"] integerValue];
        if (code == 200) {
            NSArray *dataArr = responseDict[@"data"];
            if (![PLVFdUtil checkDictionaryUseable:responseDict] ||
                ![PLVFdUtil checkArrayUseable:dataArr]) {
                [self callBackFailBlock:failure
                           modul:PLVFErrorCodeModulChat
                            code:PLVFChatErrorCodeGetSwitch_DataError
                            info:responseDict
                       errorDescription:PLVFDLocalizableString(@"数据解析失败")];
            } else {
                NSMutableDictionary *switchInfo = [NSMutableDictionary dictionary];
                [dataArr enumerateObjectsUsingBlock:^(NSDictionary * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                    [switchInfo setObject:obj[@"enabled"] forKey:obj[@"type"]];
                }];
                if (completion) {
                    completion(switchInfo);
                }
            }
        }else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeGetSwitch_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeGetSwitch_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChatRoomHistoryWithRoomId:(NSString *)roomId index:(NSUInteger)index size:(NSInteger)size timestamp:(NSString *)timestamp order:(BOOL)order completion:(void (^)(NSArray * _Nonnull))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"roomId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeHistory_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    PLVSocketManager *socketManager = [PLVSocketManager sharedManager];
    PLVSocketUserType userType = socketManager.userType;
    BOOL specialType = (userType == PLVSocketUserTypeGuest ||
                        userType == PLVSocketUserTypeTeacher ||
                        userType == PLVSocketUserTypeAssistant ||
                        userType == PLVSocketUserTypeManager);
    
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    plv_dict_set(params, @"roomId", roomId);
    plv_dict_set(params, @"index", @(index));
    plv_dict_set(params, @"size", @(size));
    plv_dict_set(params, @"order", order ? @"asc" : @"desc");
    plv_dict_set(params, @"userId", socketManager.viewerId);
    if ([PLVFdUtil checkStringUseable:timestamp]) {
        plv_dict_set(params, @"timestamp", timestamp);
    }
    if (!specialType && [PLVFdUtil checkStringUseable:socketManager.token]) {
        plv_dict_set(params, @"token", socketManager.token);
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/channel/chat-history-by-timestamp/get"] params:params httpMethod:PLV_HM_GET];
    if (specialType && [PLVFdUtil checkStringUseable:socketManager.token]) {
        [request setValue:socketManager.token forHTTPHeaderField:@"Authorization"];
    }
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSInteger code = PLV_SafeIntegerForDictKey(responseDict, @"code");
        if (code == 200) {
            NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
            NSArray *list = PLV_SafeArraryForDictKey(data, @"list");
            if (list) {
                completion ? completion(list) : nil;
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeHistory_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeHistory_DataError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeHistory_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChatRoomHistoryWithRoomId:(NSString *)roomId
                              startIndex:(NSUInteger)startIndex
                                endIndex:(NSInteger)endIndex
                              completion:(void (^)(NSArray *))completion
                                 failure:(void (^)(NSError *))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"roomId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeHistory_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSMutableURLRequest *request;
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    plv_dict_set(params, @"start", @(startIndex));
    plv_dict_set(params, @"end", @(endIndex));
    plv_dict_set(params, @"fullMessage", @(1));
    plv_dict_set(params, @"hasCustom", @(1));
   
    if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
        !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
          [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        plv_dict_set(params, @"channelId", roomId);
        plv_dict_set(params, @"appId", liveConfig.appId);
        NSString *url = @"https://api.polyv.net/live/v3/channel/chat/get-history-contents";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        plv_dict_set(params, @"roomId", roomId);
        NSString *timestamp = [NSString stringWithFormat:@"%lld", (long long)round([PLVFdUtil curTimeInterval] / 1000 / 60 / 60)];
        plv_dict_set(params, @"timestamp", timestamp);
        NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) { // NSLiteralSearch ：区分大小写
            return [obj1 compare:obj2 options:NSLiteralSearch];
        }];
        NSMutableString *paramStr = [NSMutableString string];
        for (NSString *key in keys) {
            [paramStr appendFormat:@"%@%@", key, params[key]];
        }
        
        NSString *plain = [NSString stringWithFormat:@"polyvChatSign%@polyvChatSign",paramStr];
        NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
        plv_dict_set(params, @"sign", sign);
        NSString *urlString = @"front/getChatHistory";
        if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
            [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
            urlString = @"front/chat-history/get";
        }
        request = [PLVFNetworkUtil requestWithURLString:[private_apichat_domain stringByAppendingString:urlString]
                                                 params:params
                                             httpMethod:PLV_HM_GET
                                        timeoutInterval:30.0
                                              userAgent:[PLVLiveVideoConfig userAgent]];
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        NSInteger code = PLV_SafeIntegerForDictKey(responseDict, @"code");
        if (code == 200) {
            NSArray *data = PLV_SafeArraryForDictKey(responseDict, @"data");
            if (data) {
                completion ? completion(data) : nil;
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeHistory_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeHistory_DataError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeHistory_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChatRoomListUsersWithRoomId:(NSString *)roomId
                                      page:(NSUInteger)page
                                    length:(NSUInteger)length
                                  streamer:(BOOL)streamer
                                   success:(void (^)(NSDictionary *))success
                                   failure:(void (^)(NSError *))failure {
    [self requestChatRoomListUsersWithRoomId:roomId page:page length:length sessionId:nil streamer:streamer success:success failure:failure];
}

+ (void)requestChatRoomListUsersWithRoomId:(NSString *)roomId
                                      page:(NSUInteger)page
                                    length:(NSUInteger)length
                                 sessionId:(NSString * _Nullable)sessionId
                                  streamer:(BOOL)streamer
                                   success:(void (^)(NSDictionary *))success
                                   failure:(void (^)(NSError *))failure {
    if(![PLVFdUtil checkStringUseable:roomId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeOnlineUsers_ParameterError
                           info:@"roomId is invalid"];
        return;
    }
    
    if (page < 0 || length < 0) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeOnlineUsers_ParameterError
                           info:@"parameter page and len is invalid"];
        return;
    }
    
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
        !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
          [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (![PLVFdUtil checkStringUseable:liveConfig.appId] || ![PLVFdUtil checkStringUseable:liveConfig.appSecret]) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeOnlineUsers_ParameterError
                               info:PLVFDLocalizableString(@"appId或appSecret无效")];
            return;
        }
        NSDictionary *param = @{
            @"channelId" : roomId,
            @"page" : @(page),
            @"len" : @(length),
            @"appId" : liveConfig.appId
        };
        NSMutableDictionary *muDict = [[NSMutableDictionary alloc] initWithDictionary:param];
        if (streamer) {
            [muDict setObject:@(1) forKey:@"getStatus"];
        }
        if ([PLVFdUtil checkStringUseable:sessionId]) {
            [muDict setObject:sessionId forKey:@"sessionId"];
        }
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/chat/list-users"];
        request = [PLVLiveAPIUtils requestWithURL:url params:muDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        NSDictionary *param = @{@"roomId" : roomId, @"page" : @(page), @"len" : @(length)};
        NSMutableDictionary *muDict = [[NSMutableDictionary alloc] initWithDictionary:param];
        if (streamer) {
            [muDict setObject:@(1) forKey:@"getStatus"];
        }
        if ([PLVFdUtil checkStringUseable:sessionId]) {
            [muDict setObject:sessionId forKey:@"sessionId"];
        }
        NSString *urlString = [private_apichat_domain stringByAppendingString:@"front/listUsers"];
        if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
            [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
            urlString = [private_apichat_domain stringByAppendingString:@"front/list-users/get"];
        }
        request = [PLVLiveAPIUtils requestWithURL:urlString params:muDict httpMethod:PLV_HM_GET];
    }
    [PLVLiveVideoAPI requestSecureApiDictionary:request completion:^(NSDictionary *responseDict) {
        NSArray *userListArray = responseDict[@"userlist"];
        if (userListArray && [userListArray isKindOfClass:[NSArray class]]) {
            if (success) {
                success(responseDict);
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeOnlineUsers_DataError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeOnlineUsers_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChatRoomListKickedWithRoomId:(NSString *)roomId
                                    success:(void (^)(NSArray *responseArray))success
                                    failure:(void (^)(NSError *error))failure {
    if(![PLVFdUtil checkStringUseable:roomId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeKickedUsers_ParameterError
                           info:@"roomId is invalid"];
        return;
    }
    NSDictionary *paramDict = @{ @"roomId" : roomId };
    NSString *urlString = [private_apichat_domain stringByAppendingString:@"admin/listKicked"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlString params:paramDict httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            NSArray *userListArray = responseDict[@"data"];
            if (userListArray && [userListArray isKindOfClass:[NSArray class]]) {
                if (success) {
                    success(userListArray);
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeKickedUsers_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeKickedUsers_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeKickedUsers_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestEmotionImagesWithRoomId:(NSUInteger)roomId
                             accountId:(NSString *)accountId
                                  page:(NSUInteger)page
                                  size:(NSUInteger)size
                               success:(void (^)(NSDictionary *data))success
                               failure:(void (^ _Nullable)(NSError *error))failure {
    
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]||
        ![PLVFdUtil checkStringUseable:accountId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"userId、appId或appSecret无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulChat code:PLVFChatErrorCodeEmotionImages_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    if(roomId < 0 || page < 0 || size < 0 ) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeEmotionImages_ParameterError
                           info:@"parameter is invalid"];
        return;
    }
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
        !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
          [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
        NSString *url = @"https://api.polyv.net/live/v4/chat/get-emotion-images";
        NSDictionary *param = @{@"channelId" : @(roomId),
                                @"appId" : appId,
                                @"accountId" : accountId,
                                @"pageNumber" : @(page),
                                @"pageSize" : @(size)};
        request = [PLVLiveAPIUtils requestWithURL:url params:param SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        NSDictionary *param = @{@"roomId" : @(roomId),
                                @"accountId" : accountId,
                                @"page" : @(page),
                                @"size" : @(size)};
        NSMutableDictionary *muDict = [[NSMutableDictionary alloc] initWithDictionary:param];
        NSString *urlString = [private_apichat_domain stringByAppendingString:@"front/getEmotionImages"];
        if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
            [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
            urlString = [private_apichat_domain stringByAppendingString:@"front/emotion-images/get"];
        }
        request = [PLVFNetworkUtil requestWithURLString:urlString
                                                 params:[muDict copy]
                                             httpMethod:PLV_HM_GET
                                        timeoutInterval:30.0
                                              userAgent:[PLVLiveVideoConfig userAgent]];
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            NSArray *imageArray;
            NSMutableDictionary *muDict = [[NSMutableDictionary alloc] initWithDictionary:responseDict];
            if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
                !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
                  [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
                imageArray = responseDict[@"data"][@"contents"];
            } else {
                imageArray = responseDict[@"data"][@"list"];
            }
            if (imageArray && [imageArray isKindOfClass:[NSArray class]]) {
                if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
                    !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
                      [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
                    NSDictionary *dataDict = @{@"list": imageArray}; // 把contents字段换成list，为了使得SDK向下兼容
                    [muDict setObject:dataDict forKey:@"data"];
                }
                if (success) {
                    success(muDict);
                }
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeEmotionImages_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeEmotionImages_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeEmotionImages_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)likeWithChannelId:(NSUInteger)channelId viewerId:(NSString *)viewerId times:(NSUInteger)times completion:(void (^)(void))completion failure:(void (^)(NSError *error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulChat code:PLVFChatErrorCodeLike_ParameterError info:errorMessage errorDescription:errorMessage];
        return;
    }
    
    if (channelId <= 0) {
        NSString *errorMessage = PLVFDLocalizableString(@"channelId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeLike_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channels/%lu/like", (unsigned long)channelId];
    NSString * urlEncodeUserId = [PLVFdUtil URLEncodedString:viewerId];
    NSDictionary *paramDict = @{
        @"appId" : appId,
        @"viewerId" : urlEncodeUserId,
        @"times" : @(times)
    };
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (completion) {
                completion();
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeLike_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeLike_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)uploadImage:(UIImage *)image
          imageName:(NSString *)imageName
          channelId:(NSString *)channelId
           progress:(void (^)(float))progressBlock
            success:(void (^)(NSDictionary *tokenDict, NSString *key, NSString *imageName))successBlock
               fail:(void (^)(NSError *error))failBlock {
    [self uploadImage:image
            imageData:nil
            imageName:imageName
            channelId:channelId
             progress:progressBlock
              success:successBlock
                 fail:failBlock];
}

+ (void)uploadImage:(UIImage *)image
          imageName:(NSString *)imageName
           progress:(void (^)(float))progressBlock
            success:(void (^)(NSDictionary *tokenDict, NSString *key, NSString *imageName))successBlock
               fail:(void (^)(NSError *error))failBlock {
    [self uploadImage:image
            imageData:nil
            imageName:imageName
            channelId:nil
             progress:progressBlock
              success:successBlock
                 fail:failBlock];
}

+ (void)uploadImageData:(NSData *)imageData
              imageName:(NSString *)imageName
              channelId:(NSString *)channelId
               progress:(void (^)(float))progressBlock
                success:(void (^)(NSDictionary *tokenDict, NSString *key, NSString *imageName))successBlock
                   fail:(void (^)(NSError *error))failBlock {
    [self uploadImage:nil
            imageData:imageData
            imageName:imageName
            channelId:channelId
             progress:progressBlock
              success:successBlock
                 fail:failBlock];
}

+ (void)uploadImageData:(NSData *)imageData
              imageName:(NSString *)imageName
               progress:(void (^)(float))progressBlock
                success:(void (^)(NSDictionary *tokenDict, NSString *key, NSString *imageName))successBlock
                   fail:(void (^)(NSError *error))failBlock {
    [self uploadImage:nil
            imageData:imageData
            imageName:imageName
            channelId:nil
             progress:progressBlock
              success:successBlock
                 fail:failBlock];
}

+ (void)increaseViewerWithChannelId:(NSString *)channelId times:(NSUInteger)times completion:(void (^)(NSInteger viewers))completion failure:(void (^)(NSError *error))failure {
    if (channelId.length == 0 ||
        ![PLVFdUtil checkStringUseable:[PLVLiveVideoConfig sharedInstance].appId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = @"https://api.polyv.net/live/v3/channel/watch/increase-page-viewer";
        NSDictionary *paramDict = @{
            @"channelId" : channelId,
            @"appId" : liveConfig.appId,
            @"times" : (times > 0 ? @(times) : @(1))
        };
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSInteger viewers = [responseDict[@"data"] integerValue];
                if (completion) {
                    completion(viewers);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestPageViewWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary *data))completion failure:(void (^)(NSError *error))failure {
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/public/watch/channel/info/encrypt/get-pv?channelId=%@", channelId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:nil SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        id data = [responseDict objectForKey:@"data"];
        id dataDecryptedObject = [PLVFNetworkUtil parseDecryptData:data];
        if ([PLVFdUtil checkDictionaryUseable:dataDecryptedObject]) {
            completion ? completion(dataDecryptedObject) : nil;
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFInitErrorCodeChannelInfo_DataError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFInitErrorCodeChannelInfo_CodeError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestChatRoomRemindHistoryWithRoomId:(NSString *)roomId startIndex:(NSUInteger)startIndex endIndex:(NSInteger)endIndex completion:(void (^)(NSArray * _Nonnull))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"roomId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeRemindHistory_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSMutableURLRequest *request;
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    plv_dict_set(params, @"start", @(startIndex));
    plv_dict_set(params, @"end", @(endIndex));
    plv_dict_set(params, @"source", @"extend");
    plv_dict_set(params, @"hide", @(0));
    plv_dict_set(params, @"fullMessage", @(1));
    
    if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi &&
        !([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
          [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        plv_dict_set(params, @"channelId", roomId);
        plv_dict_set(params, @"appId", liveConfig.appId);
        NSString *url = @"https://api.polyv.net/live/v4/chat/get-chat-history";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
    } else {
        plv_dict_set(params, @"roomId", roomId);
        NSString *timestamp = [NSString stringWithFormat:@"%lld", (long long)round([PLVFdUtil curTimeInterval] / 1000 / 60 / 60)];
        plv_dict_set(params, @"timestamp", timestamp);
        NSArray *keys = [params.allKeys sortedArrayUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) { // NSLiteralSearch ：区分大小写
            return [obj1 compare:obj2 options:NSLiteralSearch];
        }];
        NSMutableString *paramStr = [NSMutableString string];
        for (NSString *key in keys) {
            [paramStr appendFormat:@"%@%@", key, params[key]];
        }
        
        NSString *plain = [NSString stringWithFormat:@"polyvChatSign%@polyvChatSign",paramStr];
        NSString *sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
        plv_dict_set(params, @"sign", sign);
        NSString *urlString = @"front/getChatHistory";
        if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
            [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
            urlString = @"front/chat-history/get";
        }
        request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:urlString] params:params httpMethod:PLV_HM_GET];
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        NSInteger code = PLV_SafeIntegerForDictKey(responseDict, @"code");
        if (code == 200) {
            NSArray *data = PLV_SafeArraryForDictKey(responseDict, @"data");
            if (data) {
                completion ? completion(data) : nil;
            } else {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeRemindHistory_DataError
                                   info:responseDict];
            }
        } else {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeRemindHistory_CodeError
                               info:responseDict];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeRemindHistory_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}
         
+ (void)requestChatRoomQuestionHistoryWithRoomId:(NSString *)roomId page:(NSUInteger)page pageSize:(NSInteger)pageSize completion:(void (^)(NSDictionary *data))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"roomId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeQuestionHistory_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }

    __weak typeof(self) weakSelf = self;
    void(^failureBlock)(NSError * _Nonnull error, BOOL isGetToken) = ^(NSError * _Nonnull error, BOOL isGetToken) {
        PLVFChatErrorCode code = isGetToken ? PLVFChatErrorCodeQuestionHistory_GetTokenError : PLVFChatErrorCodeQuestionHistory_DataError;
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [weakSelf callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:code
                               info:error.localizedDescription];
        } else {
            [weakSelf callBackFailBlock:failure networkError:error];
        }
    };
    
    PLVSocketManager *socketManager = [PLVSocketManager sharedManager];
    [PLVLiveVideoAPI getChatTokenWithChannelId:roomId userId:socketManager.viewerId role:socketManager.userTypeString groupId:socketManager.liveParam4 completion:^(NSDictionary * _Nonnull data) {
        NSString *token = PLV_SafeStringForDictKey(data, @"token");
        NSMutableURLRequest *request;
        NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
        plv_dict_set(params, @"page", @(page));
        plv_dict_set(params, @"size", @(pageSize));
        plv_dict_set(params, @"token", token);
        plv_dict_set(params, @"userId", socketManager.viewerId);
        
        if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi ||
            ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
             [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
            NSString *url = @"https://api.polyv.net/live/inner/v3/sdk/get-quiz-list";
            plv_dict_set(params, @"channelId", roomId);
            request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
                return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:@"polyv_sdk_api_innor"];
            } httpMethod:PLV_HM_POST];
        } else {
            plv_dict_set(params, @"roomId", roomId);
            request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/channel/question-list/get"] params:params httpMethod:PLV_HM_GET];
        }
        
        [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
            if ([PLVFdUtil checkDictionaryUseable:responseDict] &&
                PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
                completion ? completion(responseDict) : nil;
            } else {
                [weakSelf callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulChat
                                   code:PLVFChatErrorCodeQuestionHistory_DataError
                                   info:responseDict];
            }
        } fail:^(NSError *error) {
            failureBlock(error, NO);
        }];
    } failure:^(NSError * _Nonnull error) {
        failureBlock(error, YES);
    }];
}

+ (void)requestNewestRedpackWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary *data))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"channelId无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeNewestRedpack_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeNewestRedpack_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    plv_dict_set(paramDict, @"channelId", channelId);
    plv_dict_set(paramDict, @"appId", appId);
    
    NSString *url = @"https://api.polyv.net/live/v4/channel/red-pack/rain/get-newest";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSInteger code = [responseDict[@"code"] integerValue];
        if (code == 200) {
            NSDictionary *data = responseDict[@"data"];
            if (![PLVFdUtil checkDictionaryUseable:responseDict] ||
                ![PLVFdUtil checkDictionaryUseable:data]) {
                [self callBackFailBlock:failure
                           modul:PLVFErrorCodeModulChat
                            code:PLVFChatErrorCodeNewestRedpack_DataError
                            info:responseDict
                       errorDescription:PLVFDLocalizableString(@"数据解析失败")];
            } else {
                if (completion) {
                    completion(data);
                }
            }
        }else {
            NSDictionary *errorDict = PLV_SafeDictionaryForDictKey(responseDict, @"error");
            NSString *errorDesc = PLV_SafeStringForDictKey(errorDict, @"desc") ?: @"";
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeNewestRedpack_CodeError
                               info:responseDict
                   errorDescription:errorDesc];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeNewestRedpack_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)requestRedpackReceiveCacheWithChannelId:(NSString *)channelId
                                       viewerId:(NSString *)viewerId
                                      redpackId:(NSString *)redpackId
                                     redCacheId:(NSString *)redCacheId
                                     completion:(void (^)(NSDictionary *data))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:viewerId] ||
        ![PLVFdUtil checkStringUseable:redpackId] ||
        ![PLVFdUtil checkStringUseable:redCacheId]) {
        NSString *errorMessage = PLVFDLocalizableString(@"参数为空");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeRedpackReceiveCache_ParameterError
                           info:errorMessage
               errorDescription:errorMessage];
        return;
    }
    
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeRedpackReceiveCache_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    plv_dict_set(paramDict, @"viewerId", viewerId);
    plv_dict_set(paramDict, @"redpackId", redpackId);
    plv_dict_set(paramDict, @"redCacheId", redCacheId);
    
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/watch/channel/red-pack/%@/receive/cache?appId=%@", channelId, appId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict signConfig:^(PLVLiveSignCreator * _Nonnull signConfig) {
        signConfig.uppercase = YES;
        signConfig.SHA256 = YES;
        signConfig.signatureNonce = YES;
        signConfig.encrypt = YES;
        signConfig.requestEncrypt = YES;
        signConfig.signSplicedURL = YES;
    } plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        NSMutableDictionary *muParamDict = [NSMutableDictionary dictionaryWithDictionary:paramDict];
        [muParamDict setObject:appId forKey:@"appId"];
        return [PLVLiveAPIUtils createStringWithParamDict:[muParamDict copy]];
    } httpMethod:PLV_HM_POST];
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSInteger code = [responseDict[@"code"] integerValue];
        if (code == 200) {
            NSDictionary *data = responseDict[@"data"];
            if (![PLVFdUtil checkDictionaryUseable:responseDict] ||
                ![PLVFdUtil checkDictionaryUseable:data]) {
                [self callBackFailBlock:failure
                           modul:PLVFErrorCodeModulChat
                            code:PLVFChatErrorCodeRedpackReceiveCache_DataError
                            info:responseDict
                       errorDescription:PLVFDLocalizableString(@"数据解析失败")];
            } else {
                if (completion) {
                    completion(data);
                }
            }
        }else {
            NSDictionary *errorDict = PLV_SafeDictionaryForDictKey(responseDict, @"error");
            NSString *errorDesc = PLV_SafeStringForDictKey(errorDict, @"desc") ?: @"";
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeRedpackReceiveCache_CodeError
                               info:responseDict
                   errorDescription:errorDesc];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeRedpackReceiveCache_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 连麦相关

+ (void)rtcEnabled:(NSUInteger)channelId completion:(void (^)(BOOL))completion failure:(void (^)(NSError *))failure {
    if (channelId == 0) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSDictionary *param = @{
            @"appId" : [PLVLiveVideoConfig sharedInstance].appId,
            @"channelId" : @(channelId)
        };
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v3/channel/get-rtc-enabled" params:param SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
                if (completion) {
                    BOOL rtcEnabled = [@"Y" isEqualToString:responseDict[@"data"]];
                    completion(rtcEnabled);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestLinkMicStatusWithRoomId:(NSUInteger)roomId completion:(void (^)(NSString *, NSString *))completion failure:(void (^)(NSError *))failure {
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/microphone-status/get"] params:@{@"roomId" : @(roomId)} httpMethod:PLV_HM_GET];//获取连麦状态接口
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        NSString *url = @"https://api.polyv.net/live/v3/channel/chat/get-microphone-status";
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (![PLVFdUtil checkStringUseable:liveConfig.appId] || ![PLVFdUtil checkStringUseable:liveConfig.appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
            return;
        }
        NSDictionary *paramDict = @{
            @"appId" : liveConfig.appId,
            @"channelId" : @(roomId)
        };
        
        request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];//获取连麦状态接口
    } else {
        request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/getMicrophoneStatus"] params:@{@"roomId" : @(roomId)} httpMethod:PLV_HM_GET];//获取连麦状态接口
    }
    
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        if (responseDict) {
            if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi && responseDict[@"data"] && [responseDict[@"data"] isKindOfClass:[NSDictionary class]]) {
                NSDictionary *dataDict = responseDict[@"data"];
                completion(dataDict[@"status"], dataDict[@"type"]);
            } else {
                completion(responseDict[@"status"], responseDict[@"type"]);
            }
        } else if (responseCont) {
            NSString *status = @"open";
            NSString *type = @"audio";
            if (![responseCont containsString:status]) {
                status = @"close";
            }
            if (![responseCont containsString:type]) {
                type = @"video";
            }
            completion(status, type);
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeDecodeFail desc:responseArr.firstObject];
        }
    } fail:failure];
}

+ (void)requestLinkMicOnlineListWithRoomId:(NSString *)roomId sessionId:(NSString *)sessionId completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    [self requestLinkMicOnlineListWithRoomId:roomId sessionId:sessionId getSubRooms:0 completion:completion failure:failure];
}

+ (void)requestLinkMicOnlineListWithRoomId:(NSString *)roomId sessionId:(NSString *)sessionId getSubRooms:(BOOL)getSubRooms completion:(void (^)(NSDictionary *))completion failure:(void (^)(NSError *))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
        return;
    }
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        NSString *url = @"https://api.polyv.net/live/v4/chat/get-interact-status";
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        if (![PLVFdUtil checkStringUseable:liveConfig.appId] || ![PLVFdUtil checkStringUseable:liveConfig.appSecret]) {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
            return;
        }
        NSDictionary *param = @{
            @"appId" : liveConfig.appId,
            @"channelId" : roomId,
            @"getStatus" : @(1),
            @"sessionId" : [PLVFdUtil checkStringUseable:sessionId] ? sessionId : @"",
            @"toGetSubRooms" : (getSubRooms ? @(1) : @(0))
        };
        
        request = [PLVLiveAPIUtils requestWithURL:url params:param SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];//获取连麦在线信息接口
    } else {
        NSMutableDictionary *params = [[NSMutableDictionary alloc] initWithCapacity:3];
        [params setObject:roomId forKey:@"roomId"];
        if ([PLVFdUtil checkStringUseable:sessionId]) {
            [params setObject:sessionId forKey:@"sessionId"];
            [params setObject:@(1) forKey:@"getStatus"];
        }
        if (getSubRooms) {
            [params setObject:@(1) forKey:@"toGetSubRooms"];
        }
        request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/getInteractStatus"] params:params httpMethod:PLV_HM_GET];//获取连麦在线信息接口
    }
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if (responseDict && ![PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
            completion(responseDict);
        } else if (responseDict && responseDict[@"data"] && [PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
            completion(responseDict[@"data"]);
        }else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeDecodeFail desc:responseArr.firstObject];
        }
    } fail:failure];
}

+ (void)requestViewerIdLinkMicIdRelate:(nonnull NSString *)channelId viewerId:(nonnull NSString *)viewerId linkMicId:(nonnull NSString *)linkMicId completion:(nullable void (^)(void))completion failure:(nullable void (^)(NSError * _Nonnull))failure{
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:viewerId] || ![PLVFdUtil checkStringUseable:linkMicId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/agora/relation"];
        NSDictionary *paramDict = @{
            @"channelId" : channelId,
            @"viewerId" : viewerId,
            @"uid" : linkMicId,
            @"appId" : liveConfig.appId
        };
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_POST];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (completion) {
                    completion();
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestLinkMicRemoveUser:(NSString *)channelId userId:(NSString *)userId linkMicId:(NSString *)linkMicId completion:(void (^)(BOOL))completion failure:(void (^)(NSError *))failure {
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:userId] || ![PLVFdUtil checkStringUseable:linkMicId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
        return;
    }
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/mic/remove-user"];
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    plv_dict_set(paramDict, @"appId", liveConfig.appId);
    plv_dict_set(paramDict, @"roomId", channelId);
    plv_dict_set(paramDict, @"userIds", userId);
    plv_dict_set(paramDict, @"rtcUserId", linkMicId);
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_POST];
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
            if (completion) {
                BOOL success = PLV_SafeBoolForValue(responseDict[@"data"]);
                completion(success);
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
        }
    } fail:failure];
}

#pragma mark - SIP相关

+ (void)requestSIPInfoWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary  *data))completion failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/channel/sip/get-info"];
        NSDictionary *paramDict = @{
            @"channelId" : channelId,
            @"appId" : liveConfig.appId,
        };
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:NO plainBlock:^NSString *(NSString *timestamp) {
            return [NSString stringWithFormat:@"%@appId%@channelId%@timestamp%@%@", liveConfig.appSecret, liveConfig.appId, channelId, timestamp, liveConfig.appSecret];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                if (completion) {
                    completion(data);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:^(NSError *error) {
            if (error.code == PLVNetworkErrorCodeDecodeFail) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInteraction
                                   code:PLVFInteractionErrorCodePostLottery_DataError
                                   info:error.localizedDescription
                       errorDescription:@"数据解析失败"];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }];
    }
}

+ (void)requestSIPDialLinesListWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary *data))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/channel/sip/dial-lines/list"];
        NSDictionary *paramDict = @{
            @"channelId" : channelId,
            @"appId" : liveConfig.appId,
        };
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:NO plainBlock:^NSString *(NSString *timestamp) {
            return [NSString stringWithFormat:@"%@appId%@channelId%@timestamp%@%@", liveConfig.appSecret, liveConfig.appId, channelId, timestamp, liveConfig.appSecret];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (PLV_SafeIntegerForDictKey(responseDict, @"code") == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                if (completion) {
                    completion(data);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:^(NSError *error) {
            if (error.code == PLVNetworkErrorCodeDecodeFail) {
                [self callBackFailBlock:failure
                                  modul:PLVFErrorCodeModulInteraction
                                   code:PLVFInteractionErrorCodePostLottery_DataError
                                   info:error.localizedDescription
                       errorDescription:@"数据解析失败"];
            } else {
                [self callBackFailBlock:failure networkError:error];
            }
        }];
    }
}

#pragma mark - 抽奖

+ (void)postLotteryWithData:(NSDictionary *)data completion:(void (^)(void))completion failure:(void (^)(NSError *error))failure {
    if (!data) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInteraction
                           code:PLVFInteractionErrorCodePostLottery_ParameterError
                           info:nil
               errorDescription:nil];
        return;
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/add-receive-info"] params:data httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (completion) {
                completion();
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodePostLottery_CodeError info:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInteraction
                               code:PLVFInteractionErrorCodePostLottery_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)newPostLotteryWithData:(NSDictionary *)data completion:(void (^)(void))completion failure:(void (^)(NSError *error))failure {
    if (!data) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInteraction
                           code:PLVFInteractionErrorCodePostLottery_ParameterError
                           info:nil
               errorDescription:nil];
        return;
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/v2/add-receive-info"] params:data httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (completion) {
                completion();
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodePostLottery_CodeError info:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInteraction
                               code:PLVFInteractionErrorCodePostLottery_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

+ (void)giveUpReceiveWithChannelId:(NSString *)channelId userId:(NSString *)userId completion:(void (^)(NSString *))completion failure:(void (^)(NSError *error))failure {
    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:userId]) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulInteraction
                           code:PLVFInteractionErrorCodeGiveUpLottery_ParameterError
                           info:nil
               errorDescription:nil];
        return;
    }
        
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:[private_apichat_domain stringByAppendingString:@"front/give-up-receive"] params:@{@"channelId" : channelId, @"userId" : userId} httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
            if (completion) {
                completion(responseDict[@"data"]);
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulInteraction code:PLVFInteractionErrorCodeGiveUpLottery_CodeError info:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure
                              modul:PLVFErrorCodeModulInteraction
                               code:PLVFInteractionErrorCodeGiveUpLottery_DataError
                               info:error.localizedDescription
                   errorDescription:PLVFDLocalizableString(@"数据解析失败")];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 礼物打赏

+ (void)requestRewardWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary*))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    NSDictionary *paramDict = @{
        @"appId" : liveConfig.appId,
        @"channelId" : channelId
    };
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v4/watch/channel/donate/setting/get" params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                completion(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

+ (void)requestPointSettingWithChannelId:(NSUInteger)channelId completion:(nullable void (^)(NSDictionary *))completion failure:(nullable void (^)(NSError *))failure{
    if (!channelId) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/donate/get-point-setting"];
        NSDictionary *paramDict = @{
            @"appId" : liveConfig.appId,
            @"channelId" : @(channelId)
        };
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        } httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (completion) {
                    completion(responseDict);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestViewerPointWithViewerId:(NSString *)viewerId nickName:(NSString *)nickName channelId:(NSUInteger)channelId completion:(nullable void (^)(NSString *))completion failure:(nullable void (^)(NSError *))failure{
    if (!channelId || !viewerId || !nickName) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/donate/point/get"];
        NSDictionary *param = @{
                    @"appId" : [PLVLiveVideoConfig sharedInstance].appId,
                    @"channelId" : @(channelId),
                    @"viewerId" : viewerId,
                    @"nickname" : nickName
                };
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:param SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            NSMutableDictionary *muParamDict = [[NSMutableDictionary alloc] initWithDictionary:paramDict];
            [muParamDict setObject:nickName forKey:@"nickname"];
            [muParamDict setObject:viewerId forKey:@"viewerId"];
            return [PLVLiveAPIUtils createStringWithParamDict:[muParamDict copy]];
        } httpMethod:PLV_HM_POST];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSNumber *data = responseDict[@"data"];
                if (completion) {
                   if ([data isKindOfClass:[NSNumber class]]) {
                        completion([data stringValue]);
                   } else if ([data isKindOfClass:[NSString class]]) {
                        NSString *dataString = responseDict[@"data"];
                        NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
                        formatter.numberStyle = NSNumberFormatterDecimalStyle;
                        NSNumber *num = [formatter numberFromString:dataString];
                        if (num != nil) {
                            completion([num stringValue]);
                        } else {
                            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"data is not number"];
                        }
                   } else {
                        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"data is not number"];
                   }
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestViewerRewardPointWithViewerId:(NSString *)viewerId nickName:(NSString *)nickName avatar:(NSString *)avatar goodId:(NSInteger)goodId goodNum:(NSInteger)goodNum channelId:(NSUInteger)channelId completion:(nullable void (^)(NSString *))completion failure:(nullable void (^)(NSError *))failure{
    if (!channelId || !viewerId || !nickName || !avatar || !goodId || !goodNum) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/donate/point/reward"];
        NSDictionary *paramDict = @{
            @"appId" : liveConfig.appId,
            @"channelId" : @(channelId),
            @"viewerId" : viewerId,
            @"nickname" : nickName,
            @"avatar" : avatar,
            @"goodId" : @(goodId),
            @"goodNum" :  @(goodNum)
        };
        
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES requestEncrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
        }  httpMethod:PLV_HM_POST];
        
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSString *data = PLV_SafeStringForDictKey(responseDict, @"data");
                if (completion && [PLVFdUtil checkStringUseable:data]) {
                    completion(data);
                }else{
                    [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"data is nil"];
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        } fail:failure];
    }
}

+ (void)requestViewerFreeDonateRewardWithViewerId:(NSString *)viewerId nickName:(NSString *)nickName avatar:(NSString *)avatar goodId:(NSInteger)goodId goodNum:(NSInteger)goodNum channelId:(NSUInteger)channelId sessionId:(NSString *)sessionId completion:(nullable void (^)(void))completion failure:(nullable void (^)(NSError *))failure {
    if (!channelId || !viewerId || !nickName || !avatar || !goodId || !goodNum || !sessionId) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
        NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/watch/channel/donate/donate-good?appId=%@", liveConfig.appId];
        NSDictionary *paramDict = @{
            @"authType" : @"none",
            @"donateType" : @"GIFT",
            @"channelId" : @(channelId),
            @"viewerId" : viewerId,
            @"nickname" : nickName,
            @"avatar" : avatar,
            @"goodId" : @(goodId),
            @"goodNum" :  @(goodNum),
            @"sessionId" : sessionId
        };
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict signConfig:^(PLVLiveSignCreator * _Nonnull signConfig) {
            signConfig.uppercase = YES;
            signConfig.SHA256 = YES;
            signConfig.signatureNonce = YES;
            signConfig.encrypt = YES;
            signConfig.requestEncrypt = [PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt;
            signConfig.signSplicedURL = YES;
        } plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            NSMutableDictionary *muParamDict = [NSMutableDictionary dictionaryWithDictionary:paramDict];
            [muParamDict setObject:liveConfig.appId forKey:@"appId"];
            return [PLVLiveAPIUtils createStringWithParamDict:[muParamDict copy]];
        } httpMethod:PLV_HM_POST];
        
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (responseDict) {
                NSNumber *code = responseDict[@"code"];
                if (code.integerValue == 200) {
                    if (completion) {
                        completion();
                    }
                } else {
                    [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
            }
        } fail:failure];
    }
}

+ (void)requestCardPushInfoWithChannelId:(NSString *)channelId cardPushId:(NSString *)cardPushId completion:(void (^)(NSDictionary *cardDict))completion failure:(nullable void (^)(NSError *))failure {
    if (!channelId || !cardPushId) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:nil];
    } else {
        NSString *timestamp = [PLVFdUtil curTimeStamp];
        NSString *url = [NSString stringWithFormat:@"https://liveimages.videocc.net/cardPush/%@/%@.json?%@", channelId, cardPushId, timestamp];
        NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
        [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
            if (responseDict) {
                NSNumber *code = responseDict[@"code"];
                if (code.integerValue == 200) {
                    if (completion) {
                        completion(responseDict[@"data"]);
                    }
                } else {
                    [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
            }
        } fail:failure];
    }
}

+ (void)requestDonateWithChannelId:(NSString *)channelId completion:(void (^)(NSDictionary*))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    NSDictionary *paramDict = @{
        @"appId" : liveConfig.appId,
        @"channelId" : channelId
    };
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:@"https://api.polyv.net/live/v4/channel/donate/get" params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                completion(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

+ (void)updateDonateGiftWithChannelId:(NSString *)channelId donateGiftEnabled:(BOOL)donateGiftEnabled completion:(nullable void (^)(void))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    
    NSMutableDictionary *mParam = [NSMutableDictionary dictionary];
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    mParam[@"appId"] = liveConfig.appId;
    mParam[@"channelId"] = channelId;
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    mParam[@"timestamp"] = timestamp;
    NSString *urlStr = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/channel/donate/gift/update?channelId=%@&appId=%@&timestamp=%@", channelId, liveConfig.appId, timestamp];
    if ([PLVLiveVideoConfig sharedInstance].realEnableSignatureNonce) {
        NSString *signatureNonceString = [[NSUUID UUID] UUIDString];
        mParam[@"signatureNonce"] = signatureNonceString;
        urlStr = [NSString stringWithFormat:@"%@&signatureNonce=%@", urlStr, signatureNonceString];
    }
    if ([PLVLiveVideoConfig sharedInstance].realEnableResponseEncrypt) {
        mParam[@"encryptResponseType"] = @(1);
        urlStr = [NSString stringWithFormat:@"%@&encryptResponseType=1", urlStr];
    }
    NSString *sign;
    NSString *plain;
    if ([PLVLiveVideoConfig sharedInstance].realEnableSha256) {
        mParam[@"signatureMethod"] = @"SHA256";
        urlStr = [NSString stringWithFormat:@"%@&signatureMethod=SHA256", urlStr];
        plain = [PLVLiveAPIUtils createStringWithParamDict:mParam];
        sign = [[PLVDataUtil sha256String:plain] uppercaseString];
    } else {
        plain = [PLVLiveAPIUtils createStringWithParamDict:mParam];
        sign = [[PLVDataUtil md5HexDigest:plain] uppercaseString];
    }
    urlStr = [NSString stringWithFormat:@"%@&sign=%@", urlStr ,sign];
    
    NSString *basicSetting = [NSString stringWithFormat:@"{\"donateGiftEnabled\":\"%@\"}", donateGiftEnabled ? @"Y" : @"N"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt) {
        basicSetting = [[PLVDataUtil AES256EncryptData:[basicSetting dataUsingEncoding:NSUTF8StringEncoding] withKey:liveConfig.appSecret iv:[PLVKeyUtil getApiUtilsIv]] base64EncodedStringWithOptions:0];
        basicSetting = [basicSetting stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLUserAllowedCharacterSet]];
    }
    
    NSData *jsonData = [basicSetting dataUsingEncoding:NSUTF8StringEncoding];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlStr httpMethod:PLV_HM_POST];
    [request setHTTPBody:jsonData];
    [request setValue:@"application/json;charset=utf8" forHTTPHeaderField:@"Content-Type"];
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt) {
        [request setValue:@"1" forHTTPHeaderField:@"x-e-type"];
    }
    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (completion) {
                    completion();
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

#pragma mark - 直播回放
/// 获取视频弹幕信息
+ (void)loadDanmuWithVid:(NSString *)vid time:(NSString *)time msgId:(NSUInteger)msgId limit:(NSUInteger)limit completion:(void (^)(NSArray *danmuArr, NSError *error))completion {
    if (!vid || !time || limit == 0) {
        completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")]);
        return;
    }
    
    NSString *url = @"https://api.polyv.net/v2/danmu";
    NSDictionary *param = @{@"vid":vid, @"limit":@(limit), @"origin":@"playback", @"time":time, @"id":@(msgId)};
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:param httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestArray:request completion:^(NSArray *responseArr) {
        if (completion) {
            completion(responseArr, nil);
        }
    } fail:^(NSError *error) {
        if (completion) {
            completion(nil, error);
        }
    }];
}

/// 新增回放弹幕
+ (void)addDanmuWithVid:(NSString *)vid msg:(NSString *)msg time:(NSString *)time sessionId:(NSString *)sessionId msgType:(NSString *)msgType user:(NSString *)user completion:(void (^)(NSDictionary *, NSError *))completion {
    if (!vid || !msg || !time || !msgType || !user) {
        completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")]);
        return;
    }
    if (!sessionId) {
        sessionId = @"";
    }
    NSString *url = @"https://api.polyv.net/v2/danmu/add";
    NSDictionary *param = @{@"vid":vid, @"msg":msg, @"time":time, @"sessionId":sessionId, @"origin":@"playback", @"msgType":msgType, @"user":user};
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:param httpMethod:PLV_HM_POST];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            if ([responseDict[@"status"] isEqualToString:@"success"]) {
                if (completion) {
                    completion(responseDict[@"data"], nil);
                }
            } else {
                if (completion) {
                    completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]]);
                }
            }
        } else {
            if (completion) {
                completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."]);
            }
        }
    } fail:^(NSError *error) {
        if (completion) {
            completion(nil, error);
        }
    }];
}

+ (void)requestPlaybackEnableWithChannelId:(NSString *)channelId appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(BOOL enable , NSError *))completion {
    if (!completion) {
        return;
    }
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
        !completion ?: completion(NO, error);
        return;
    }

    if (![PLVFdUtil checkStringUseable:channelId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"参数错误")];
        !completion ?: completion(NO, error);
        return;
    }
    
    NSString *urlStr = @"https://api.polyv.net/live/v3/channel/playback/get-enabled";
    NSMutableDictionary *mParams = [NSMutableDictionary dictionary];
    mParams[@"channelId"] = channelId;
    mParams[@"appId"] = appId;

    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlStr params:mParams SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSString *playbackEnabled = responseDict[@"data"];
                if ([playbackEnabled isEqualToString:@"Y"]) {
                    completion(YES, nil);
                }else {
                    completion(NO, nil);
                }
            } else {
                completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]]);
            }
        } else {
            completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."]);
        }
    } fail:^(NSError *error) {
        completion(nil, error);
    }];
}

+ (void)requestPlaybackListWithChannelId:(NSString *)channelId listType:(NSString *)listType page:(NSUInteger)page pageSize:(NSUInteger)pageSize appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(PLVPlaybackListModel * _Nullable model, NSError * _Nullable error))completion {
    if (!completion) {
        return;
    }
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
        !completion ?: completion(nil, error);
        return;
    }

    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:listType]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"参数错误")];
        !completion ?: completion(nil, error);
        return;
    }
    
    NSString *urlStr = @"https://api.polyv.net/live/v3/channel/playback/list/sdk";
    NSMutableDictionary *mParams = [NSMutableDictionary dictionary];
    mParams[@"channelId"] = channelId;
    mParams[@"listType"] = listType;
    mParams[@"appId"] = appId;
    mParams[@"page"] = @(page).stringValue;
    if (pageSize > 0) {
        mParams[@"pageSize"] = @(pageSize).stringValue;
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlStr params:mParams SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if ([responseDict[@"status"] isEqualToString:@"success"]) {
            PLVPlaybackListModel *model = [[PLVPlaybackListModel alloc] initWithDictionary:responseDict[@"data"]];
            if (model) {
                completion(model, nil);
            } else {
                completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeJsonDecodeFail desc:PLVFDLocalizableString(@"获取回放列表数据解析失败")]);
            }
        } else {
            completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]]);
        }
    } fail:^(NSError *error) {
        completion(nil, error);
    }];
}

+ (void)requestPlaybackList:(NSString *)channelId listType:(NSString *)listType page:(NSUInteger)page pageSize:(NSUInteger)pageSize appId:(NSString *)appId appSecret:(nonnull NSString *)appSecret completion:(nonnull void (^)(PLVPlaybackListModel *, NSError *))completion {
    if (!completion) {
        return;
    }
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
        !completion ?: completion(nil, error);
        return;
    }

    if (![PLVFdUtil checkStringUseable:channelId] ||
        ![PLVFdUtil checkStringUseable:listType]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"参数错误")];
        !completion ?: completion(nil, error);
        return;
    }
    
    NSString *urlStr = [NSString stringWithFormat:@"https://api.polyv.net/live/v2/channel/recordFile/%@/playback/list",channelId];
    NSMutableDictionary *mParams = [NSMutableDictionary dictionary];
    mParams[@"listType"] = listType;
    mParams[@"appId"] = appId;
    mParams[@"page"] = @(page).stringValue;
    if (pageSize > 0) {
        mParams[@"pageSize"] = @(pageSize).stringValue;
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:urlStr params:mParams SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if ([responseDict[@"status"] isEqualToString:@"success"]) {
            PLVPlaybackListModel *model = [[PLVPlaybackListModel alloc] initWithDictionary:responseDict[@"data"]];
            if (model) {
                completion(model, nil);
            } else {
                completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeJsonDecodeFail desc:PLVFDLocalizableString(@"获取回放列表数据解析失败")]);
            }
        } else {
            completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]]);
        }
    } fail:^(NSError *error) {
        completion(nil, error);
    }];
}

+ (void)requestLivePlaybackSectionEnableWithChannelId:(NSString *)channelId completion:(void (^)(BOOL))completion failure:(void (^)(NSError * _Nonnull))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    
    NSString *url = [NSString stringWithFormat:@"https://live.polyv.cn/v2/watch/channel/detail?channelId=%@",channelId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSString *sectionEnabled = responseDict[@"data"][@"channelPlayback"][@"sectionEnabled"];
                if ([sectionEnabled isEqualToString:@"Y"]) {
                    completion(YES);
                }else {
                    completion(NO);
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

+ (void)requestLivePlaybackSectionListWithChannelId:(NSString *)channelId videoId:(NSString *)videoId completion:(void (^)(NSArray<PLVLivePlaybackSectionModel *> *sectionList, NSError *error))completion {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    [self requestLivePlaybackSectionListWithChannelId:channelId videoId:videoId appId:liveConfig.appId appSecret:liveConfig.appSecret completion:completion];
}
+ (void)requestLivePlaybackSectionListWithChannelId:(NSString *)channelId videoId:(NSString *)videoId appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(NSArray<PLVLivePlaybackSectionModel *> *sectionList, NSError *error))completion {
    if (!completion) {
        return;
    }
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:videoId] || ![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")]);
        return;
    }
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        NSString *url = [NSString stringWithFormat:@"https://apichat.polyv.net/front/get-ppt-records/playback/%@/%@.json",channelId,videoId];
       request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        NSDictionary *params = @{
            @"channelId" : channelId,
            @"videoId" : videoId,
            @"appId" : appId
        };
        NSString *url = @"https://api.polyv.net/live/v4/chat/ppt-records/playback";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
        } httpMethod:PLV_HM_GET];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://apichat.polyv.net/ppt-records/playback/%@/%@.json",channelId,videoId];
       request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    }
    
    [PLVLiveVideoAPI requestSecureApiArray:request completion:^(NSArray *responseArr) {
        if (responseArr) {
            NSMutableArray *sectionList = [[NSMutableArray alloc] initWithCapacity:[responseArr count]];
            for (NSDictionary *dic in responseArr) {
                PLVLivePlaybackSectionModel *section = [[PLVLivePlaybackSectionModel alloc] initWithDictionary:dic];
                if (section) {
                    [sectionList addObject:section];
                }
            }
            completion(sectionList, nil);
        } else {
            completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."]);
        }
    }fail:^(NSError *error) {
        completion(nil, error);
    }];
}

+ (void)requestChannelPlaybackInfoWithChannelId:(NSString *)channelId appId:(NSString *)appId appSecret:appSecret vid:(NSString * _Nullable)vid playbackType:(NSString * _Nullable)playbackType completion:(void (^)(PLVChannelPlaybackInfoModel * _Nullable channelPlaybackInfo))completion failure:(void (^)( NSError * _Nullable error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] ||
       ![PLVFdUtil checkStringUseable:appId] ||
       ![PLVFdUtil checkStringUseable:appSecret]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    NSMutableDictionary *params = [[NSMutableDictionary alloc] init];
    [params setObject:channelId forKey:@"channelId"];
    [params setObject:appId forKey:@"appId"];
    if ([PLVFdUtil checkStringUseable:vid]) {
        [params setObject:vid forKey:@"vid"];
    }
    if ([PLVFdUtil checkStringUseable:playbackType]) {
        [params setObject:playbackType forKey:@"playbackType"];
    }
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/playback/sdk"];
    NSMutableURLRequest * request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");;
                PLVChannelPlaybackInfoModel *channelPlaybackInfo = [[PLVChannelPlaybackInfoModel alloc]initWithDictionary:data];
                completion(channelPlaybackInfo);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
    
}

+ (void)requestChannelPlaybackInfoWithChannelId:(NSString *)channelId completion:(void (^)(PLVChannelPlaybackInfoModel * _Nullable channelPlaybackInfo))completion failure:(void (^)( NSError * _Nullable error))failure {
    if (![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    
    NSString *url = [NSString stringWithFormat:@"https://live.polyv.cn/v2/watch/channel/detail?channelId=%@",channelId];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");;
                PLVChannelPlaybackInfoModel *channelPlaybackInfo = [[PLVChannelPlaybackInfoModel alloc]initWithDictionary:PLV_SafeDictionaryForDictKey(data, @"channelPlayback") ];
                completion(channelPlaybackInfo);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

+ (void)requestLiveRecordSectionListWithChannelId:(NSString *)channelId fileId:(NSString *)fileId completion:(void (^)(NSArray<PLVLivePlaybackSectionModel *> *sectionList, NSError *error))completion {
    PLVLiveVideoConfig *liveConfig = [PLVLiveVideoConfig sharedInstance];
    [self requestLiveRecordSectionListWithChannelId:channelId fileId:fileId appId:liveConfig.appId appSecret:liveConfig.appSecret completion:completion];
}

+ (void)requestLiveRecordSectionListWithChannelId:(NSString *)channelId fileId:(NSString *)fileId appId:(NSString *)appId appSecret:(NSString *)appSecret completion:(void (^)(NSArray<PLVLivePlaybackSectionModel *> *sectionList, NSError *error))completion {
    if (!completion) {
        return;
    }
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:fileId] || ![PLVFdUtil checkStringUseable:appId] || ![PLVFdUtil checkStringUseable:appSecret]) {
        completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")]);
        return;
    }
    NSMutableURLRequest *request;
    if ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
        [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2) {
        NSString *url = [NSString stringWithFormat:@"https://apichat.polyv.net/front/get-ppt-records/record/%@/%@.json",channelId,fileId];
        request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    } else if ([PLVLiveVideoConfig sharedInstance].realEnableSecureApi) {
        NSDictionary *params = @{
            @"channelId" : channelId,
            @"fileId" : fileId,
            @"appId" : appId
        };
        NSString *url = @"https://api.polyv.net/live/v4/chat/ppt-records/record";
        request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
            return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
        } httpMethod:PLV_HM_GET];
    } else {
        NSString *url = [NSString stringWithFormat:@"https://apichat.polyv.net/ppt-records/record/%@/%@.json",channelId,fileId];
        request = [PLVLiveAPIUtils requestWithURL:url httpMethod:PLV_HM_GET];
    }
    
    [PLVLiveVideoAPI requestSecureApiArray:request completion:^(NSArray *responseArr) {
        if (responseArr) {
            NSMutableArray *sectionList = [[NSMutableArray alloc] initWithCapacity:[responseArr count]];
            for (NSDictionary *dic in responseArr) {
                PLVLivePlaybackSectionModel *section = [[PLVLivePlaybackSectionModel alloc] initWithDictionary:dic];
                if (section) {
                    [sectionList addObject:section];
                }
            }
            completion(sectionList, nil);
        } else {
            completion(nil, [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."]);
        }
    }fail:^(NSError *error) {
        completion(nil, error);
    }];
}

+ (void)updatePlaybackSettingWithChannelId:(NSString *)channelId playbackEnabled:(BOOL)playbackEnabled  completion:(nullable void (^)(void))completion failure:(void (^)(NSError * _Nonnull))failure {
    if(![PLVFdUtil checkStringUseable:channelId]) {
        [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"提交参数错误")];
        return;
    }
    NSDictionary *params = @{
        @"channelId" : channelId,
        @"appId" : [PLVLiveVideoConfig sharedInstance].appId,
        @"playbackEnabled" : playbackEnabled ? @"Y" : @"N"
    };
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/playback/set-setting"];
    NSMutableURLRequest * request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                if (completion) {
                    completion();
                }
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"error"][@"desc"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:failure];
}

#pragma mark - 跑马灯

+ (void)loadCustomMarquee:(NSURL *)url withChannelId:(NSUInteger)channelId userId:(NSString *)userId code:(NSString *)code completion:(void (^)(BOOL, NSDictionary *))completion failure:(void (^)(NSError *))failure {
    if (!url ||
        ![url isKindOfClass:NSURL.class] ||
        channelId == 0 ||
        ![PLVFdUtil checkStringUseable:userId]) {
        failure([PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"URL自定义类型跑马灯提交参数错误")]);
        return;
    }
    
    NSString *timestamp = [PLVFdUtil curTimeStamp];
    if (![PLVFdUtil checkStringUseable:code]) {
        code = @"";
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url.absoluteString params:@{@"vid":@(channelId), @"uid":userId, @"code":code, @"t":timestamp} httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSArray *keyArrs = @[@"vid",@"uid",@"username",@"code",@"t",@"msg",@"fontSize",@"fontColor",@"speed",@"filter",@"setting",@"alpha",@"filterAlpha",@"filterColor",@"blurX",@"blurY",@"interval",@"lifeTime",@"tweenTime",@"strength",@"show"];
        NSMutableDictionary *mDict = [NSMutableDictionary dictionaryWithDictionary:responseDict];
        [mDict setObject:@(channelId) forKey:@"vid"];
        [mDict setObject:userId forKey:@"uid"];
        [mDict setObject:code forKey:@"code"];
        [mDict setObject:timestamp forKey:@"t"];
        NSMutableString *signStr = [NSMutableString string];
        [keyArrs enumerateObjectsUsingBlock:^(NSString*  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            [signStr appendString:[NSString stringWithFormat:@"%@=%@&",obj,mDict[obj]]];
        }];
        [signStr deleteCharactersInRange:NSMakeRange(signStr.length-1, 1)];
        NSString *sign = [PLVDataUtil md5HexDigest:signStr];
        if ([responseDict[@"sign"] isEqualToString:sign]) {
            completion(YES, responseDict);
        } else {
            NSString *msg = responseDict[@"msg"];
            if (!msg) {
                msg = PLVFDLocalizableString(@"校验失败");
            }
            completion(NO, @{@"msg":msg});
        }
    } fail:failure];
}

#pragma mark - 商品

+ (void)loadCommodityList:(NSUInteger)channelId rank:(NSUInteger)rank count:(NSUInteger)count completion:(void (^)(NSUInteger, NSArray<PLVCommodityModel *> *))completion failure:(void (^)(NSError *))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"appId或appSecret无效") };
        NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120101 userInfo:userInfo];
        if (failure) {
            failure(failError);
        }
        return;
    }
    
    NSString *requestUrl = @"https://api.polyv.net/live/v3/channel/product/getListByRank";
    NSMutableDictionary *params = [NSMutableDictionary dictionaryWithCapacity:5];
    [params setObject:appId forKey:@"appId"];
    [params setObject:@(channelId) forKey:@"channelId"];
    [params setObject:@(count) forKey:@"count"];
    if (rank > 0) {
        [params setObject:@(rank) forKey:@"rank"];
    }
    
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:requestUrl params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (![responseDict[@"status"] isEqualToString:@"success"]) {
            NSDictionary *userInfo = nil;
            NSString *errorMsg = responseDict[@"message"];
            if (errorMsg && [errorMsg isKindOfClass:[NSString class]] && errorMsg.length > 0) {
                userInfo = @{ NSLocalizedDescriptionKey : responseDict[@"message"] };
            }
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120101 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSDictionary *data = responseDict[@"data"];
        if (![data isKindOfClass:NSDictionary.class]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"返回数据格式异常") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120101 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSArray *content = data[@"content"];
        if (![content isKindOfClass:NSArray.class]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"返回数据格式异常") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120101 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSMutableArray *datas = [NSMutableArray arrayWithCapacity:content.count];
        for (NSDictionary *dict in content) {
            PLVCommodityModel *model = [PLVCommodityModel commodityModelWithDict:dict];
            if (model) {
                [datas addObject:model];
            }
        }
        
        NSUInteger total = [data[@"total"] integerValue];
        
        completion(total, datas);
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"数据解析失败") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120101 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 海报
/// 获取邀请海报设置
+ (void)requestInvitePosterWithChannelId:(NSString *)channelId success:(void (^) (NSDictionary *respondDict))success failure:(void (^)(NSError * error))failure {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"appId或appSecret无效")];
        failure ? failure(error) : nil;
        return;
    }

    if (![PLVFdUtil checkStringUseable:channelId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"参数错误")];
        failure ? failure(error) : nil;
        return;
    }
        
    NSMutableDictionary * mParam = [NSMutableDictionary dictionary];
    mParam[@"channelId"] = channelId;
    mParam[@"appId"] = appId;
    NSString *url = [NSString stringWithFormat:@"https://api.polyv.net/live/v4/channel/invite/setting/get"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:mParam SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict appsecret:appSecret];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:success fail:^(NSError *error) {
        failure ? failure(error) : nil;
    }];
}

#pragma mark - 推流
/// 更新频道直播状态至结束接口
+ (void)requestChannelLivestatusEndWithChannelId:(NSString *)channelId
                                          stream:(NSString *)stream
                                         success:(void (^)(NSString *))success
                                         failure:(void (^)(NSError * error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] ||
       ![PLVFdUtil checkStringUseable:stream]) {
        [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeLivestatusEndFailed_ParameterError info:nil];
        return;
    }
    
    NSMutableDictionary * params = [NSMutableDictionary dictionary];
    params[@"channelId"] = channelId;
    params[@"stream"] = stream;
    params[@"version"] = [NSString stringWithFormat:@"%@",[PLVLiveVideoConfig sharedInstance].playerVersion];
    params[@"appId"] = [PLVLiveVideoConfig sharedInstance].appId;
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/client/live-status/end"];
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:params SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        NSNumber *code = responseDict[@"code"];
        if (code.integerValue == 200) {
            if (success) {
                NSString *dataString = PLV_SafeStringForDictKey(responseDict, @"data");
                success(dataString);
            }
        } else {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeLivestatusEndFailed_CodeError info:responseDict[@"message"]];
        }
    } fail:^(NSError * error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failure modul:PLVFErrorCodeModulLink code:PLVFLinkErrorCodeLivestatusEndFailed_DataError info:nil];
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark - 分组

+ (void)requestGroupListWithChannelId:(NSString *)channelId
                                appId:(NSString *)appId
                         channelToken:(NSString *)channelToken
                              success:(void (^)(NSArray<NSDictionary *> *groupArray))success
                              failure:(void (^ _Nullable)(NSError * error))failure {
    if(![PLVFdUtil checkStringUseable:channelId] ||
       ![PLVFdUtil checkStringUseable:appId] ||
       ![PLVFdUtil checkStringUseable:channelToken]) {
        NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"channelId、appId或channelToken无效") };
        NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120102 userInfo:userInfo];
        if (failure) {
            failure(failError);
        }
        return;
    }
    NSMutableDictionary * mParam = [NSMutableDictionary dictionary];
    mParam[@"channelId"] = channelId;
    mParam[@"appId"] = appId;
    mParam[@"channelToken"] = channelToken;
    mParam[@"timestamp"] = [PLVFdUtil curTimeStamp];
    
    NSString * url = [NSString stringWithFormat:@"https://api.polyv.net/live/v3/channel/group/list"];
    NSMutableURLRequest * request = [PLVLiveAPIUtils requestWithURL:url params:mParam httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (![responseDict[@"status"] isEqualToString:@"success"] ||
            [responseDict[@"code"] integerValue] != 200) {
            NSDictionary *userInfo = nil;
            NSString *errorMsg = responseDict[@"message"];
            if (errorMsg && [errorMsg isKindOfClass:[NSString class]] && errorMsg.length > 0) {
                userInfo = @{ NSLocalizedDescriptionKey : responseDict[@"message"] };
            }
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120102 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        NSArray *data = responseDict[@"data"];
        if (![data isKindOfClass:[NSArray class]]) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"返回数据格式异常") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120102 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
            return;
        }
        
        success(data);
        
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            NSDictionary *userInfo = @{ NSLocalizedDescriptionKey : PLVFDLocalizableString(@"数据解析失败") };
            NSError *failError = [NSError errorWithDomain:@"net.polyv.PLVLiveScenesSDK" code:2120102 userInfo:userInfo];
            if (failure) {
                failure(failError);
            }
        } else {
            [self callBackFailBlock:failure networkError:error];
        }
    }];
}

#pragma mark 观看页用户列表

+ (void)requestViewerListWithRoomId:(NSString *)roomId
                            success:(void (^)(NSDictionary *viewerListDict))success
                            failure:(void (^ _Nullable)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:roomId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"获取观看页用户列表，roomId无效")];
        failure ? failure(error) : nil;
        return;
    }
    
    NSString * url = [NSString stringWithFormat:@"https://apichat.polyv.net/front/viewer/user-list/%@",roomId];
    NSMutableURLRequest * request = [PLVLiveAPIUtils requestWithURL:url params:nil httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                success(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:failure networkError:error];
    }];
}

+ (void)requestViewerListWithRoomId:(NSString *)roomId
                            groupId:(NSString *)groupId
                            success:(void (^)(NSDictionary *viewerListDict))success
                            failure:(void (^ _Nullable)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:roomId] ||
        ![PLVFdUtil checkStringUseable:groupId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"获取观看页用户列表，roomId无效 或 groupId无效")];
        failure ? failure(error) : nil;
        return;
    }
    
    NSString * url = [NSString stringWithFormat:@"https://apichat.polyv.net/front/viewer/user-list/%@/%@", roomId, groupId];
    NSMutableURLRequest * request = [PLVLiveAPIUtils requestWithURL:url params:nil httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                success(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:failure networkError:error];
    }];
}

#pragma mark 多会场

+ (void)requestMultiMeetingListWithChannelId:(NSString *)channelId
                               mainChannelId:(NSString *)mainChannelId
                                  pageNumber:(NSUInteger)pageNumber
                                    pageSize:(NSUInteger)pageSize
                                     success:(void (^) (NSDictionary *multiMeetingList))success
                                     failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:channelId] || ![PLVFdUtil checkStringUseable:mainChannelId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"获取观看页用户列表，channelId无效 或 mainChannelId无效")];
        failure ? failure(error) : nil;
        return;
    }
    
    if (pageNumber < 0 || pageSize < 0) {
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeOnlineUsers_ParameterError
                           info:@"parameter page and size is invalid"];
        return;
    }
    
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeNewestRedpack_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    plv_dict_set(paramDict, @"channelId", channelId);
    plv_dict_set(paramDict, @"mainChannelId", mainChannelId);
    plv_dict_set(paramDict, @"pageNumber", @(pageNumber));
    plv_dict_set(paramDict, @"pageSize", @(pageSize));
    plv_dict_set(paramDict, @"appId", appId);
    
    NSString * url = @"https://api.polyv.net/live/v4/channel/multi-meeting/page";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];

    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                success(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:failure networkError:error];
    }];
}

+ (void)requestMultiMeetingLiveStatusWithMainChannelId:(NSString *)mainChannelId
                                               success:(void (^) (NSArray *multiMeetingLiveStatusList))success
                                               failure:(void (^)(NSError * error))failure {
    if (![PLVFdUtil checkStringUseable:mainChannelId]) {
        NSError *error = [PLVFNetworkUtil failWitErrorCode:PLVNetworkErrorCodeParamsInvalid desc:PLVFDLocalizableString(@"获取观看页用户列表，channelId无效 或 mainChannelId无效")];
        failure ? failure(error) : nil;
        return;
    }
    
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failure
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeNewestRedpack_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] init];
    plv_dict_set(paramDict, @"channelId", mainChannelId);
    plv_dict_set(paramDict, @"appId", appId);
    
    NSString * url = @"https://api.polyv.net/live/v4/channel/multi-meeting/live-status";
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:paramDict SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];

    
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (responseDict) {
            NSString *status = PLV_SafeStringForDictKey(responseDict, @"status");
            if ([status isEqualToString:@"success"]) {
                NSArray *data = PLV_SafeArraryForDictKey(responseDict, @"data");
                success(data);
            } else {
                [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeRequestFail desc:responseDict[@"message"]];
            }
        } else {
            [PLVFNetworkUtil fail:failure errorCode:PLVNetworkErrorCodeResponseFail desc:@"responseDict be nil."];
        }
    } fail:^(NSError *error) {
        [self callBackFailBlock:failure networkError:error];
    }];
}

#pragma mark - Private methods

+ (void)uploadImage:(UIImage * _Nullable)image
          imageData:(NSData * _Nullable)imageData
          imageName:(NSString *)imageName
          channelId:(NSString *)channelId
           progress:(void (^)(float))progressBlock
            success:(void (^)(NSDictionary *tokenDict, NSString *key, NSString *imageName))successBlock
               fail:(void (^)(NSError *error))failBlock {
    NSString *appId = [PLVLiveVideoConfig sharedInstance].appId;
    NSString *appSecret = [PLVLiveVideoConfig sharedInstance].appSecret;
    
    if (![PLVFdUtil checkStringUseable:appId] ||
        ![PLVFdUtil checkStringUseable:appSecret]) {
        NSString *errorMessage = PLVFDLocalizableString(@"appId或appSecret无效");
        [self callBackFailBlock:failBlock
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeImageUploadToken_ParameterError
                           info:errorMessage];
        return;
    }
    
    if (!image && !imageData) {
        NSString *errorMessage = PLVFDLocalizableString(@"图片不可为空");
        [self callBackFailBlock:failBlock
                          modul:PLVFErrorCodeModulChat
                           code:PLVFChatErrorCodeImageUploadToken_ParameterError
                           info:errorMessage];
        return;
    }
    
    NSString *url = @"https://api.polyv.net/live/v3/common/image/get-token";
    NSDictionary *param = @{@"appId": appId, @"channelId": channelId};
    NSMutableURLRequest *request = [PLVLiveAPIUtils requestWithURL:url params:param SHA256:YES signatureNonce:YES encrypt:YES plainBlock:^NSString * _Nonnull(NSString * _Nonnull timestamp, NSDictionary * _Nonnull paramDict) {
        return [PLVLiveAPIUtils createStringWithParamDict:paramDict];
    } httpMethod:PLV_HM_GET];
    [PLVFNetworkUtil requestDictionary:request completion:^(NSDictionary *responseDict) {
        if (((NSNumber *)responseDict[@"code"]).integerValue == 200) {
            NSDictionary *tokenDict = responseDict[@"data"];
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                NSString *key = [NSString stringWithFormat:@"%@%@", tokenDict[@"dir"], imageName];
                NSDictionary *params = @{
                    @"key" : key,
                    @"policy" : tokenDict[@"policy"],
                    @"OSSAccessKeyId" :  tokenDict[@"accessid"],
                    @"signature" : tokenDict[@"signature"],
                    @"callback" : tokenDict[@"encodedCallback"],
                    @"success_action_status" : @"200"};

                void(^complete)(BOOL success, NSError *error) = ^(BOOL success, NSError *error) {
                    if (success) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            if (successBlock){
                                successBlock (tokenDict, key, imageName);
                            }
                        });
                    } else {
                        NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulChat
                                                                                         code:PLVFChatErrorCodeImageUpload_Failure];
                        [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:nil];
                        
                        dispatch_async(dispatch_get_main_queue(), ^{
                            if (failBlock) {
                                failBlock (error);
                            }
                        });
                    }
                };
                
                void(^progress)(float fractionCompleted) = ^(float fractionCompleted) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        if (progressBlock){
                            progressBlock (fractionCompleted);
                        }
                    });
                };
                
                if (imageData) {
                    [[PLVImageUpload shareUtil] uploadImageData:imageData imageName:imageName parameters:params completed:complete progress:progress];
                } else {
                    [[PLVImageUpload shareUtil] uploadImage:image imageName:imageName parameters:params completed:complete progress:progress];
                }
            });
        } else {
            [self callBackFailBlock:failBlock
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeImageUploadToken_CodeError
                               info:responseDict
                   errorDescription:responseDict[@"message"]];
        }
    } fail:^(NSError *error) {
        if (error.code == PLVNetworkErrorCodeDecodeFail) {
            [self callBackFailBlock:failBlock
                              modul:PLVFErrorCodeModulChat
                               code:PLVFChatErrorCodeImageUploadToken_DataError
                               info:error.localizedDescription];
        } else {
            [self callBackFailBlock:failBlock networkError:error];
        }
    }];
}

// 限制登录接口每秒登录10次
+ (BOOL)isRequestAllowed {
    [_kLoginLock lock];
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    if (currentTime - _lastRequestTime >= 1.0) {
        _requestCount = 0;
        _lastRequestTime = currentTime;
    }
    
    if (_requestCount >= 10) {
        [_kLoginLock unlock];
        return NO;
    }
    _requestCount++;
    [_kLoginLock unlock];
    return YES;
}

#pragma mark 通用

/// 调用接口（非网络问题）失败时使用此方法上报错误码及执行回调 block。
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
                    modul:(PLVFErrorCodeModul)modul
                     code:(NSInteger)code
                     info:(id)info {
    [self callBackFailBlock:failBlock modul:modul code:code info:info errorDescription:nil];
}

/// 调用接口（非网络问题）失败时使用此方法上报错误码及执行回调 block。同时使用 errorDescription 字段替换原先默认的 localizedDescription 属性
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
                    modul:(PLVFErrorCodeModul)modul
                     code:(NSInteger)code
                     info:(id)info
         errorDescription:(NSString *)errorDesc {
    NSError *failError = [[PLVWErrorManager sharedManager] errorWithModul:modul code:code];
    
    if (errorDesc && [errorDesc isKindOfClass:[NSString class]] && errorDesc.length > 0) {
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorDesc forKey:NSLocalizedDescriptionKey];
        NSError *userError = [NSError errorWithDomain:failError.domain code:failError.code userInfo:userInfo];
        failError = userError;
    }

    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:failError.code information:info];
    
    if (failBlock) {
        failBlock(failError);
    }
}

/// 调用接口（因网络问题）失败时使用此方法生成错误码并上报及执行回调 block
+ (void)callBackFailBlock:(void (^)(NSError *))failBlock
             networkError:(NSError *)error {
    NSError *myError = [[PLVWErrorManager sharedManager] errorWithModul:PLVFErrorCodeModulHttp code:PLVFHttpErrorCodeAllNetworkFailure];
    NSString *information = [error.description stringByReplacingOccurrencesOfString:@"&" withString:@" "];
    [[PLVWLogReporterManager sharedManager] reportWithErrorCode:myError.code information:information];
    
    if (failBlock) {
        NSString *errorMessage = error.localizedDescription ?: PLVFDLocalizableString(@"网络错误");
        NSDictionary *userInfo = [NSDictionary dictionaryWithObject:errorMessage forKey:NSLocalizedDescriptionKey];
        NSError *UserError = [NSError errorWithDomain:myError.domain code:myError.code userInfo:userInfo];
        failBlock(UserError);
    }
}

/// 调用安全接口 异步网络请求数组
+ (void)requestSecureApiArray:(NSURLRequest *)request completion:(void (^)(NSArray *responseArr))completion fail:(void (^)(NSError *error))fail {
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkArrayUseable:responseArr]) {
            responseArr = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        if (![PLVLiveVideoConfig sharedInstance].realEnableSecureApi ||
            ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
             [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
            completion ? completion(responseArr) : nil;
        } else {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSArray *data = PLV_SafeArraryForDictKey(responseDict, @"data");
                if (completion) {
                    completion(data);
                }
            } else {
                [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
            }
        }
    } fail:fail];
}

/// 调用安全接口 异步网络请求字典
+ (void)requestSecureApiDictionary:(NSURLRequest *)request completion:(void (^)(NSDictionary *responseDict))completion fail:(void (^)(NSError *error))fail {
    [PLVFNetworkUtil requestNetwork:request completion:^(NSDictionary *responseDict, NSArray *responseArr, NSString *responseCont) {
        if ([PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2 &&
            ![PLVFdUtil checkDictionaryUseable:responseDict]) {
            responseDict = [PLVFNetworkUtil parseDecryptData:responseCont];
        }
        if (![PLVLiveVideoConfig sharedInstance].realEnableSecureApi ||
            ([PLVLiveVideoConfig sharedInstance].realEnableRequestEncrypt &&
             [PLVLiveVideoConfig sharedInstance].realEncryptType == PLVEncryptType_SM2)) {
            if (completion) {
                completion(responseDict);
            }
        } else {
            NSNumber *code = responseDict[@"code"];
            if (code.integerValue == 200) {
                NSDictionary *data = PLV_SafeDictionaryForDictKey(responseDict, @"data");
                if (completion) {
                    completion(data);
                }
                else {
                    [PLVFNetworkUtil fail:fail errorCode:PLVNetworkErrorCodeResponseFail desc:responseDict[@"message"]];
                }
            }
        }
    } fail:fail];
}

+ (NSString *)URLEncodedString:(NSString *)url {
    if (![PLVFdUtil checkStringUseable:url]) { return @""; }
    NSString *charactersToEscape = @"<>|{}^`!*'();:@&=+$,/?%#[]\" ";
    NSCharacterSet *allowedCharacters = [[NSCharacterSet characterSetWithCharactersInString:charactersToEscape] invertedSet];
    NSString *encodeStr = [url stringByAddingPercentEncodingWithAllowedCharacters:allowedCharacters];
    return encodeStr;
}

@end
