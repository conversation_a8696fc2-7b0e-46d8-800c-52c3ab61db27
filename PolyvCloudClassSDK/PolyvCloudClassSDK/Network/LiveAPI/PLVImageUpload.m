//
//  PLVImageUpload.m
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/19.
//  Copyright © 2019 PLV. All rights reserved.
//

#import "PLVImageUpload.h"

static NSString *kBoundary = @"Boundary+88D30654D5F34641";
static NSString *kUrlString = @"https://liveimages.videocc.net/";

@interface PLVImageUpload () <
NSURLSessionDataDelegate
>

@property (nonatomic, strong) NSURLSession *session;
@property (nonatomic, copy) NSMutableDictionary *progressBlockMap;

@end

@implementation PLVImageUpload

#pragma mark - Getters & Setters

- (NSMutableDictionary *)progressBlockMap {
    if (!_progressBlockMap) {
        _progressBlockMap = [[NSMutableDictionary alloc] init];
    }
    return _progressBlockMap;
}

- (NSURLSession *)session {
    if (!_session) {
        NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
        _session = [NSURLSession sessionWithConfiguration:config delegate:self delegateQueue:[[NSOperationQueue alloc] init]];
    }
    return _session;
}

#pragma mark - Public

+ (instancetype)shareUtil {
    static PLVImageUpload *utils = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        utils = [[PLVImageUpload alloc] init];
    });
    return utils;
}

- (void)uploadImage:(UIImage *)image
          imageName:(NSString *)imageName
         parameters:(NSDictionary *)params
          completed:(void(^)(BOOL success, NSError *error))completedBlock
           progress:(PLVUploadProgressBlock)progressBlock {
    NSData *uploadData = [self getCompressImageData:image];
    [self uploadImageData:uploadData
                imageName:imageName
               parameters:params
                completed:completedBlock
                 progress:progressBlock];
}

- (void)uploadImageData:(NSData *)imageData
              imageName:(NSString *)imageName
             parameters:(NSDictionary *)params
              completed:(void(^)(BOOL success, NSError * _Nullable error))completedBlock
               progress:(PLVUploadProgressBlock)progressBlock {
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:kUrlString]
                                                           cachePolicy:NSURLRequestReloadIgnoringLocalCacheData
                                                       timeoutInterval:2.0f];
    [request setHTTPMethod:@"POST"];
    [request setTimeoutInterval:30];
    
    //拼接请求体数据
    NSMutableData *requestMutableData = [self requestBodyWithImageData:imageData imageName:imageName parameters:params];
    
    //设置请求体
    request.HTTPBody=requestMutableData;
    
    //设置请求头
    NSString *headStr=[NSString stringWithFormat:@"multipart/form-data; boundary=%@",kBoundary];
    [request setValue:headStr forHTTPHeaderField:@"Content-Type"];
    [request setValue:[self userAgent] forHTTPHeaderField:@"User-Agent"];
    
    NSURLSessionTask *task = [self.session dataTaskWithRequest:request completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        !completedBlock ?: completedBlock(!error, error);
    }];
    
    if (progressBlock) {
        NSString *key = [NSString stringWithFormat:@"%zd", task.taskIdentifier];
        [self.progressBlockMap setObject:progressBlock forKey:key];
    }
    
    [task resume];
}

#pragma mark - Private

- (NSMutableData *)requestBodyWithImageData:(NSData *)imageData
                                  imageName:(NSString *)imageName
                                 parameters:(NSDictionary *)params {
    NSMutableData *requestMutableData=[NSMutableData data];
    //0.拼接参数
    /*--------------------------------------------------------------------------*/
    for (NSString *key in params) {
        NSString *pair = [NSString stringWithFormat:@"--%@\r\nContent-Disposition: form-data; name=\"%@\"\r\n\r\n",kBoundary,key];
        [requestMutableData appendData:[pair dataUsingEncoding:NSUTF8StringEncoding]];
        
        id value = [params objectForKey:key];
        if ([value isKindOfClass:[NSString class]]) {
            [requestMutableData appendData:[value dataUsingEncoding:NSUTF8StringEncoding]];
        }else if ([value isKindOfClass:[NSData class]]){
            [requestMutableData appendData:value];
        }
        [requestMutableData appendData:[@"\r\n" dataUsingEncoding:NSUTF8StringEncoding]];
    }
    /*--------------------------------------------------------------------------*/
    
    //1.\r\n--Boundary+72D4CD655314C423\r\n
    // 分割符以“--”开头，后面的字随便写，只要不写中文即可，这里无需写开头的 "\r\n", 上面拼接参数结束会多出来一个 “\r\n”
    NSMutableString *myString=[NSMutableString stringWithFormat:@"--%@\r\n",kBoundary];
    
    //2. Content-Disposition: form-data; name="file"; filename="001.jpeg"\r\n
    // 这里注明服务器接收图片的参数（类似于接收用户名的userName）及服务器上保存图片的文件名
    [myString appendString:[NSString stringWithFormat:@"Content-Disposition: form-data; name=\"file\"; filename=\"%@\"\r\n", imageName]];
    
    //3. Content-Type: image/jpeg \r\n
    // 图片类型为jpeg
    [myString appendString:[NSString stringWithFormat:@"Content-Type: image/jpeg\r\n\r\n"]];
    
    //4. Content-Transfer-Encoding: binary\r\n\r\n  // 编码方式
    // [myString appendString:@"Content-Transfer-Encoding: binary\r\n\r\n"];
    
    // 转换成为二进制数据
    [requestMutableData appendData:[myString dataUsingEncoding:NSUTF8StringEncoding]];
    
    //5.文件数据部分
    [requestMutableData appendData:imageData];
    
    //6. \r\n--Boundary+72D4CD655314C423--\r\n  // 分隔符后面以"--"结尾，表明结束
    [requestMutableData appendData:[[NSString stringWithFormat:@"\r\n--%@--\r\n",kBoundary] dataUsingEncoding:NSUTF8StringEncoding]];
    /*--------------------------------------------------------------------------*/
    
    return requestMutableData;
}

- (NSData *)getCompressImageData:(UIImage *)image {
    NSData *imageData = UIImageJPEGRepresentation(image, 1.0);
    int p = 1;
    while (imageData.length > 2 * 1024 * 1024) {
        imageData = UIImageJPEGRepresentation(image, pow(0.5, p++));
    }
    return imageData;
}

- (NSString *)userAgent {
    NSString *appName = [NSBundle mainBundle].bundleIdentifier;
    NSBundle *currentBundle = [NSBundle bundleForClass:[self class]];
    NSString *sdkVersion = [[currentBundle infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    NSString *deviceName = [[UIDevice currentDevice] model];
    NSString *systemName = [[UIDevice currentDevice] systemName];
    NSString *systemVersion = [[UIDevice currentDevice] systemVersion];
    NSString *userAgent = [NSString stringWithFormat:@"%@/%@ (%@; %@ %@)", appName, sdkVersion, deviceName, systemName, systemVersion];
    return userAgent;
}

#pragma mark - NSURLSessionTaskDelegate

- (void)URLSession:(NSURLSession *)session
              task:(NSURLSessionTask *)task
   didSendBodyData:(int64_t)bytesSent
    totalBytesSent:(int64_t)totalBytesSent
totalBytesExpectedToSend:(int64_t)totalBytesExpectedToSend {
    float progress = totalBytesSent/(float)totalBytesExpectedToSend;
    
    NSString *key = [NSString stringWithFormat:@"%zd", task.taskIdentifier];
    PLVUploadProgressBlock block = self.progressBlockMap[key];
    if (block) {
        block(progress);
        if (progress == 1.0) {
            [self.progressBlockMap removeObjectForKey:key];
        }
    }
}

@end
