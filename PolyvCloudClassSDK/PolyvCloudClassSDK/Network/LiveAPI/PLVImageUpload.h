//
//  PLVImageUpload.h
//  PLVLiveScenesSDK
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/19.
//  Copyright © 2019 PLV. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^PLVUploadProgressBlock)(float fractionCompleted);

@interface PLVImageUpload : NSObject

+ (instancetype)shareUtil;

/// 图片上传方法
///  @note 图片会循环压缩，直到小于2MB
/// - Parameters:
///   - image: 待上传图片
- (void)uploadImage:(UIImage *)image
          imageName:(NSString *)imageName
         parameters:(NSDictionary *)params
          completed:(void(^)(BOOL success, NSError * _Nullable error))completedBlock
           progress:(PLVUploadProgressBlock)progressBlock;

/// 图片上传方法
///  @note 不会进行压缩，如实上传图片数据
/// - Parameters:
///   - imageData: 待上传图片数据
- (void)uploadImageData:(NSData *)imageData
              imageName:(NSString *)imageName
             parameters:(NSDictionary *)params
              completed:(void(^)(BOOL success, NSError * _Nullable error))completedBlock
               progress:(PLVUploadProgressBlock)progressBlock;

@end

NS_ASSUME_NONNULL_END
