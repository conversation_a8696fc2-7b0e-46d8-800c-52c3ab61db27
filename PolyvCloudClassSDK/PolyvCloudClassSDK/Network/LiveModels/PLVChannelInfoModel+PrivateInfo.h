//
//  PLVChannelInfoModel+PrivateInfo.h
//  PLVLiveScenesSDK
//
//  Created by Lincal on 2020/12/17.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVChannelInfoModel.h"

NS_ASSUME_NONNULL_BEGIN

/// 仅面向内部的属性及方法
@interface PLVChannelInfoModel ()

#pragma mark 当前播放地址
/// 音频的播放地址
@property (nonatomic, copy) NSString * currentAudioURLString;
/// 当前清晰度的播放地址
@property (nonatomic, copy) NSString * currentDefinitionUrl;
/// 快直播的播放地址
@property (nonatomic, copy) NSString * quickLiveUrl;
/// 当前清晰度的m3u8播放地址
@property (nonatomic, copy) NSString * currentDefinitionM3u8Url;

#pragma mark 多线路信息
/// 所有多线路信息
@property (nonatomic, strong) NSArray <NSDictionary *> * lines;

#pragma mark 多码率信息
/// 所有清晰度信息 (仅 多码率开启时 有值)
@property (nonatomic, strong) NSArray <NSDictionary *> * definitions;

/// 切换至目标线路下的目标清晰度
///
/// @note 传入的 targetLineIndex、targetDefinition 不一定就是最终的选定值。因为内部将判断传入值是否合法。
///
/// @param targetLineIndex 目标线路 (由 0 起始)
/// @param targetDefinition 目标清晰度
- (NSArray *)switchToTargetLineIndex:(NSInteger)targetLineIndex targetDefinition:(NSString *)targetDefinition;

/// 更新 SessionId
///
/// @note sessionId为空，则更新无效，即无法使用该方法进行置空；
///
/// @param sessionId 场次ID
- (void)updateChannelSessionId:(NSString *)sessionId;

@end

NS_ASSUME_NONNULL_END
