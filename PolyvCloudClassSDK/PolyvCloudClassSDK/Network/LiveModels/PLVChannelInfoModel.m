//
//  PLVChannelInfoModel.m
//  PLVLiveScenesSDK
//
//  Created by Lincal on 2020/12/17.
//  Copyright © 2020 PLV. All rights reserved.
//

#import "PLVChannelInfoModel.h"

#import "PLVLivePrivateAPI.h"
#import "PLVLiveVideoConfig.h"
#import "PLVConsoleLogger.h"
#import "PLVChannelInfoModel+PrivateInfo.h" // 仅面向内部的属性 位于此文件
#import <PLVFoundationSDK/PLVFoundationSDK.h>

@interface PLVChannelInfoModel ()

#pragma mark 原始数据
@property (nonatomic, strong) NSDictionary * originalDataDict;

#pragma mark 基础信息
@property (nonatomic, copy, nullable) NSString * accountUserId;
@property (nonatomic, copy) NSString * channelId;
@property (nonatomic, copy) NSString * sessionId;
@property (nonatomic, copy) NSString * channelName;
@property (nonatomic, assign) BOOL isOnlyAudio;
@property (nonatomic, assign) BOOL playbackKeepPlayEnabled;

#pragma mark 限制信息
@property (nonatomic, assign) PLVChannelRestrictState restrictState;
@property (nonatomic, strong) NSDictionary * originalRestrictDataDict;

#pragma mark 多线路信息
@property (nonatomic, assign) NSInteger currentLineIndex;

#pragma mark 多码率信息
@property (nonatomic, assign) BOOL multirateEnabled;
@property (nonatomic, strong) NSArray <NSString *> * definitionNamesOptions;
@property (nonatomic, copy) NSString * currentDefinition;

#pragma mark 暖场信息
@property (nonatomic, assign) PLVChannelWarmUpType warmUpType;
@property (nonatomic, copy) NSString * warmUpContentUrlString;
@property (nonatomic, copy) NSString * warmUpImageHREF;

#pragma mark 跑马灯信息
@property (nonatomic, assign) PLVChannelMarqueeType marqueeType;
@property (nonatomic, copy) NSNumber * marqueeFontSize;
@property (nonatomic, copy) NSString * marqueeFontColor;
@property (nonatomic, copy) NSString * marqueeOpacity;
@property (nonatomic, assign) BOOL marqueeAutoZoomEnabled;
@property (nonatomic, assign) NSInteger marqueeSetting;
@property (nonatomic, assign) NSInteger marqueeSpeed;

#pragma mark 防录屏水印
/// 类型
@property (nonatomic, assign) PLVChannelWatermarkType watermarkType;
/// 文字大小
@property (nonatomic, assign) PLVChannelWatermarkFontSize watermarkFontSize;
/// 是否开启水印功能
@property (nonatomic, assign) BOOL watermarkRestrict;
/// 文字内容
@property (nonatomic, copy) NSString *watermarkContent;
/// 透明度
@property (nonatomic, assign) NSInteger watermarkOpacity;

#pragma mark 播放器LOGO
@property (nonatomic, copy) NSString *logoImageUrl;
@property (nonatomic, copy) NSString *logoHref;
@property (nonatomic, assign) CGFloat logoOpacity;
@property (nonatomic, assign) NSUInteger logoPosition;
/// LOGO位置参数
@property (nonatomic, copy) NSDictionary *logoPositionDict;

#pragma mark 片头广告
@property (nonatomic, assign) PLVChannelAdvertType advertType;
@property (nonatomic, copy) NSString *advertImageUrl;
@property (nonatomic, copy) NSString *advertFlvUrl;
@property (nonatomic, copy) NSString *advertHref;
@property (nonatomic, assign) NSUInteger advertDuration;

#pragma mark 暂停广告
@property (nonatomic, copy) NSString *stopAdvertImageUrl;
@property (nonatomic, copy) NSString *stopAdvertHref;

#pragma mark 弹幕信息
@property (nonatomic, assign) BOOL closeDanmuEnable;
@property (nonatomic, assign) NSInteger barrageSpeed;

#pragma mark 流信息
@property (nonatomic, copy) NSString * streamID;
@property (nonatomic, copy) NSString * streamType;

#pragma mark Qos
@property (nonatomic, copy) NSNumber * reportFreq;
@property (nonatomic, assign) NSInteger stallingSendFrequency;

@end

@implementation PLVChannelInfoModel

#pragma mark - [ Public Methods ]
+ (instancetype)channelInfoModelWithDataDict:(NSDictionary *)dataDict {
    if (![PLVFdUtil checkDictionaryUseable:dataDict]) { return nil; }
    
    /// 初始化
    PLVChannelInfoModel * channelModel = [[PLVChannelInfoModel alloc] init];
    channelModel.originalDataDict = dataDict;
    channelModel.restrictState = PLVChannelRestrictState_Unknown;

    /// 账户信息
    channelModel.accountUserId = PLV_SafeStringForDictKey(dataDict, @"userId");
    
    /// 基础信息
    channelModel.channelId = PLV_SafeStringForDictKey(dataDict, @"channelId");
    channelModel.sessionId = PLV_SafeStringForDictKey(dataDict, @"channelSessionId");
    NSString *liveType = PLV_SafeStringForDictKey(dataDict, @"liveType");
    channelModel.isOnlyAudio = [liveType isEqualToString:@"alone"] ? NO : PLV_SafeBoolForDictKey(dataDict, @"isOnlyAudio");
    /// 基础资料
    channelModel.channelName = PLV_SafeStringForDictKey(dataDict, @"name");
    
    /// 多线路信息
    channelModel.lines = PLV_SafeArraryForDictKey(dataDict, @"lines"); // 多线路flv、m3u8 及 备用地址信息
    
    /// 快直播播放地址
    for (NSDictionary *dict in channelModel.lines) {
        NSString *cdnType = PLV_SafeStringForDictKey(dict, @"cdnType");
        if ([PLVFdUtil checkStringUseable:cdnType]) {
            if ([cdnType isEqualToString:@"tx"]) {
                channelModel.quickLiveUrl = PLV_SafeStringForDictKey(dict, @"quickLiveUrl");
            }
        }
    }
    
    /// 多码率信息
    channelModel.multirateEnabled = [dataDict[@"multirateEnabled"] boolValue];
    if (channelModel.multirateEnabled) {
        channelModel.currentDefinition = dataDict[@"multirateModel"][@"defaultDefinition"];
        channelModel.currentDefinitionUrl = dataDict[@"multirateModel"][@"defaultDefinitionUrl"];
        channelModel.definitions = dataDict[@"multirateModel"][@"definitions"];
        channelModel.currentDefinitionM3u8Url = dataDict[@"multirateModel"][@"defaultDefinitionM3u8Url"];
    }
   
    /// 暖场信息
    NSString * coverImage = PLV_SafeStringForDictKey(dataDict, @"coverImage");
    NSString * waitImage = PLV_SafeStringForDictKey(dataDict, @"waitImage");
    if ([PLVFdUtil checkStringUseable:coverImage]) {
        // 图片类型暖场
        channelModel.warmUpType = PLVChannelWarmUpType_Image;
        channelModel.warmUpContentUrlString = coverImage;
        
        NSString * coverHref = PLV_SafeStringForDictKey(dataDict, @"coverHref");
        if ([PLVFdUtil checkStringUseable:coverHref]) {
            channelModel.warmUpImageHREF = coverHref;
        }
    } else if ([PLVFdUtil checkStringUseable:waitImage]) {
        // 视频类型暖场
        channelModel.warmUpType = PLVChannelWarmUpType_Video;
        channelModel.warmUpContentUrlString = waitImage;
    } else {
        // 无暖场
        channelModel.warmUpType = PLVChannelWarmUpType_None;
    }
    
    /// 防录屏水印
    NSDictionary *watermarkDict = PLV_SafeDictionaryForDictKey(dataDict, @"channelWatermarkModel");
    if ([PLVFdUtil checkDictionaryUseable:watermarkDict]) {
        channelModel.watermarkRestrict = PLV_SafeBoolForDictKey(watermarkDict, @"watermarkRestrict");
        if (channelModel.watermarkRestrict) {
            NSString *watermarkType = PLV_SafeStringForDictKey(watermarkDict, @"watermarkType");
            if ([watermarkType isEqualToString:@"fixed"]) {
                channelModel.watermarkType = PLVChannelWatermarkType_Fixed;
            } else if ([watermarkType isEqualToString:@"nickname"]) {
                channelModel.watermarkType = PLVChannelWatermarkType_Nick;
            } else {
                channelModel.watermarkType = PLVChannelWatermarkType_None;
            }
            
            NSString *watermarkFontSize = PLV_SafeStringForDictKey(watermarkDict, @"watermarkFontSize");
            if ([watermarkFontSize isEqualToString:@"small"]) {
                channelModel.watermarkFontSize = PLVChannelWatermarkFontSize_Small;
            } else if ([watermarkFontSize isEqualToString:@"middle"]) {
                channelModel.watermarkFontSize = PLVChannelWatermarkFontSize_Middle;
            } else if ([watermarkFontSize isEqualToString:@"large"]) {
                channelModel.watermarkFontSize = PLVChannelWatermarkFontSize_Large;
            }
            
            channelModel.watermarkContent = PLV_SafeStringForDictKey(watermarkDict, @"watermarkContent");
            channelModel.watermarkOpacity = PLV_SafeIntegerForDictKey(watermarkDict, @"watermarkOpacity");
        } else {
            channelModel.watermarkType = PLVChannelWatermarkType_None;
        }
    }
    
    /// 跑马灯
    NSString * marqueeType = PLV_SafeStringForDictKey(dataDict, @"marqueeType");
    if ([PLVFdUtil checkStringUseable:marqueeType]) {
        if ([marqueeType isEqualToString:@"fixed"]) {
            channelModel.marqueeType = PLVChannelMarqueeType_Fixed;
        } else if ([marqueeType isEqualToString:@"nickname"]) {
            channelModel.marqueeType = PLVChannelMarqueeType_Nick;
        } else if ([marqueeType isEqualToString:@"diyurl"]) {
            channelModel.marqueeType = PLVChannelMarqueeType_URL;
        } else {
            channelModel.marqueeType = PLVChannelMarqueeType_None;
        }
        channelModel.marquee = PLV_SafeStringForDictKey(dataDict, @"marquee");
        channelModel.marqueeFontSize = dataDict[@"marqueeFontSize"];
        channelModel.marqueeFontColor = PLV_SafeStringForDictKey(dataDict, @"marqueeFontColor");
        channelModel.marqueeOpacity = PLV_SafeStringForDictKey(dataDict, @"marqueeOpacity");
        channelModel.marqueeAutoZoomEnabled = [dataDict[@"marqueeAutoZoomEnabled"] boolValue];
        channelModel.marqueeSetting = PLV_SafeIntegerForDictKey(dataDict, @"marqueeSetting");
        channelModel.marqueeSpeed = PLV_SafeIntegerForDictKey(dataDict, @"marqueeSpeed") / 10;
    } else {
        channelModel.marqueeType = PLVChannelMarqueeType_None;
    }
    
    /// 播放器LOGO
    NSString *logoImageUrl = PLV_SafeStringForDictKey(dataDict, @"logoImage");
    if ([PLVFdUtil checkStringUseable:logoImageUrl]) {
        channelModel.logoImageUrl = logoImageUrl;
        channelModel.logoHref = PLV_SafeStringForDictKey(dataDict, @"logoHref");
        channelModel.logoOpacity = [PLV_SafeStringForDictKey(dataDict, @"logoOpacity") floatValue];
        NSString *logoPositionKey = PLV_SafeStringForDictKey(dataDict, @"logoPosition");
        channelModel.logoPosition = [channelModel.logoPositionDict[logoPositionKey] integerValue];
    }
    
    /// 片头广告
    NSString *advertType = PLV_SafeStringForDictKey(dataDict, @"advertType");
    if ([advertType isEqualToString:@"NONE"]) {
        channelModel.advertType = PLVChannelAdvertType_None;
    } else if ([advertType isEqualToString:@"IMAGE"]) {
        channelModel.advertType = PLVChannelAdvertType_Image;
        channelModel.advertImageUrl = PLV_SafeStringForDictKey(dataDict, @"advertImage");
        channelModel.advertHref = PLV_SafeStringForDictKey(dataDict, @"advertHref");
    } else if ([advertType isEqualToString:@"FLV"]) {
        channelModel.advertType = PLVChannelAdvertType_Video;
        channelModel.advertFlvUrl = PLV_SafeStringForDictKey(dataDict, @"advertFlvUrl");
        channelModel.advertHref = PLV_SafeStringForDictKey(dataDict, @"advertHref");
    }
    channelModel.advertDuration = PLV_SafeIntegerForDictKey(dataDict, @"advertDuration");
    
    /// 暂停广告
    NSString *stopAdvertImage = PLV_SafeStringForDictKey(dataDict, @"stopAdvertImage");
    if ([PLVFdUtil checkStringUseable:stopAdvertImage]) {
        channelModel.stopAdvertImageUrl = stopAdvertImage;
        channelModel.stopAdvertHref = PLV_SafeStringForDictKey(dataDict, @"stopAdvertHref");
    }
    
    /// 弹幕模块
    channelModel.closeDanmuEnable = [dataDict[@"closeDanmuEnable"] boolValue];
    channelModel.barrageSpeed = PLV_SafeIntegerForDictKey(dataDict, @"barrageSpeed") / 10;
    
    /// 流信息
    channelModel.streamID = PLV_SafeStringForDictKey(dataDict, @"stream");
    channelModel.streamType = PLV_SafeStringForDictKey(dataDict, @"streamType");
    
    /// Qos
    channelModel.reportFreq = dataDict[@"reportFreq"];
    if (!channelModel.reportFreq || [channelModel.reportFreq isKindOfClass:[NSNull class]]) {
         // 若无值，设置默认值 10
        channelModel.reportFreq = @(10);
    } else if ([channelModel.reportFreq isKindOfClass:NSString.class]){
        // 若为字符串，则转化
        NSString * reportFreqString = (NSString *)channelModel.reportFreq;
        channelModel.reportFreq = [NSNumber numberWithInteger:reportFreqString.integerValue];
    }
    
    channelModel.stallingSendFrequency = PLV_SafeIntegerForDictKey(dataDict, @"stallingSendFrequency");
    
    channelModel.playbackKeepPlayEnabled = [PLV_SafeStringForDictKey(dataDict, @"playbackKeepPlayEnabled") isEqualToString:@"Y"];
    return channelModel;
}

- (void)updateChannelRestrictInfo:(void (^)(PLVChannelRestrictState))completion {
    __weak typeof(self) weakSelf = self;
    [PLVLivePrivateAPI getChannelRestrictInfoWithUserId:self.accountUserId channelId:self.channelId.integerValue completion:^(NSDictionary * responseDict) {
        if ([responseDict[@"canWatch"] boolValue]) {
            weakSelf.restrictState = PLVChannelRestrictState_NoneRestrict;
        } else {
            weakSelf.restrictState = PLVChannelRestrictState_PlayRestrict;
            weakSelf.originalRestrictDataDict = responseDict;
        }
        completion(weakSelf.restrictState);
    } failure:^(NSError * error) {
        weakSelf.restrictState = PLVChannelRestrictState_GetFailed;
        completion(weakSelf.restrictState);
    }];
}

#pragma mark Getter
- (NSInteger)lineNum{
    return self.lines.count;
}


#pragma mark - [ Inner Public Methods ]
- (NSArray *)switchToTargetLineIndex:(NSInteger)targetLineIndex targetDefinition:(NSString *)targetDefinition{
    /// 读取目标线路信息
    NSInteger finalLineIndex = targetLineIndex % self.lines.count;
    NSDictionary * lineDataDict = self.lines[finalLineIndex];
    self.currentLineIndex = finalLineIndex;
    
    /// 音频播放地址
    self.currentAudioURLString = lineDataDict[@"audioFlv"];
    
    /// 视频播放地址
    NSArray * finalDefinitionsArray;
    NSDictionary * multirateDataDict = lineDataDict[@"multirateModel"];
    if ([PLVFdUtil checkDictionaryUseable:multirateDataDict]) { /// 多码率开启
        NSArray * definitionsArray = multirateDataDict[@"definitions"];
        if ([PLVFdUtil checkArrayUseable:definitionsArray]) {
            for (NSDictionary * definitionDict in definitionsArray) {
                if (![PLVFdUtil checkStringUseable:targetDefinition]) {
                    self.currentDefinition = definitionDict[@"definition"];
                    self.currentDefinitionUrl = definitionDict[@"url"];
                    self.currentDefinitionM3u8Url = definitionDict[@"m3u8"];
                    if (PLVLiveVideoConfig.logLevel && PLVLiveVideoConfig.logLevel >= PLVLogLevelInfo) {
                        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"PLVChannelInfoModel - definition switch to %@",targetDefinition);
                    }
                    finalDefinitionsArray = definitionsArray;
                    break;
                } else if ([definitionDict[@"definition"] isEqualToString:targetDefinition]) {
                    self.currentDefinition = targetDefinition;
                    self.currentDefinitionUrl = definitionDict[@"url"];
                    self.currentDefinitionM3u8Url = definitionDict[@"m3u8"];
                    if (PLVLiveVideoConfig.logLevel && PLVLiveVideoConfig.logLevel >= PLVLogLevelInfo) {
                        PLV_LOG_DEBUG(PLVConsoleLogModuleTypeVerbose, @"PLVChannelInfoModel - definition switch to %@",targetDefinition);
                    }
                    finalDefinitionsArray = definitionsArray;
                    break;
                }
            }
        }
    } else { /// 多码率未开启
        self.currentDefinitionUrl = lineDataDict[@"flv"];
        self.currentDefinitionM3u8Url = lineDataDict[@"m3u8"];
    }
    
    /// 解析出 码率/清晰度 选项名数组
    NSMutableArray * definitionNamesArray = [[NSMutableArray alloc] init];
    for (NSDictionary * definition in finalDefinitionsArray) {
        [definitionNamesArray addObject:definition[@"definition"]];
    }
    if ([PLVFdUtil checkArrayUseable:definitionNamesArray]) {
        self.definitionNamesOptions = definitionNamesArray;
    }
    
    return finalDefinitionsArray;
}

- (void)updateChannelSessionId:(NSString *)sessionId{
    if ([PLVFdUtil checkStringUseable:sessionId]) { self.sessionId = sessionId; }
}


#pragma mark - [ Private Methods ]
#pragma mark Getter
- (NSDictionary *)logoPositionDict {
    if (!_logoPositionDict) {
        _logoPositionDict = @{ @"tl": @"1",
                               @"tr": @"2",
                               @"bl": @"3",
                               @"br": @"4" };
    }
    return _logoPositionDict;
}


#pragma mark - [ 数据示例 ]
/* ChannelJson 数据示例见下
{
    advertDuration = 0;
    advertFlvUrl = "";
    advertFlvVid = "";
    advertHeight = 0;
    advertHref = "";
    advertImage = "";
    advertType = NONE;
    advertWidth = 0;
    aliPullProtectExpTime = 1800;
    aliPullUrl = "https://...";
    autoPlay = 1;
    bakUrl = "https://...";
    cameraSign = "&auth_key=1598340295-0-0-54559fa80c426ec7c36d870d18cad7ca";
    ccbViewLogEncryptModel =     {
        iv = "<null>";
        key = "<null>";
        type = "<null>";
    };
    cdnType = ali;
    channelAlias = "<null>";
    channelId = 1863324;
    channelRestrictModel =     {
        authUrl = "";
        channelId = 1863324;
        userId = 96d8d4007c;
        userStatus = NORMAL;
        webSiteBlacklist = "<null>";
        webSiteWhitelist = "<null>";
    };
    channelSessionId = fqhv;
    chatTokenEnabled = Y;
    closeDanmuEnable = N;
    coverHref = "";
    coverImage = "";
    currentTimeMillis = 1598340295761;
    currentTimeSecs = 1598340295;
    cutoffHref = "";
    cutoffImage = "";
    defaultTeacherImage = "<null>";
    description = "";
    diskVideoStartTime = "-1";
    h5LowLatency = Y;
    headAdvertClickUrl = "";
    headAdvertShowUrl = "";
    isCcb = N;
    isLowLatency = N;
    isNgbEnabled = N;
    isOnlyAudio = N;
    isRightMenuEnabled = N;
    isUrlProtected = Y;
    isVr = N;
    lines =     (
                {
            audioFlv = "https://...";
            audioM3u8 = "https://...";
            bakFlv = "https://...";
            bakM3u8 = "https://...";
            cdnType = ali;
            flv = "https://...";
            m3u8 = "https://...";
            m3u81 = "";
            m3u82 = "";
            m3u83 = "";
            multirateModel = "<null>";
            rts = "<null>";
            webRtcEnabled = N;
        },
                {
            audioFlv = "https://...";
            audioM3u8 = "https://...";
            bakFlv = "https://...";
            bakM3u8 = "";
            cdnType = ws;
            flv = "https://...";
            m3u8 = "https://...";
            m3u81 = "";
            m3u82 = "";
            m3u83 = "";
            multirateModel = "<null>";
            rts = "<null>";
            webRtcEnabled = N;
        }
    );
    liveType = ppt;
    logoHref = "";
    logoImage = "https://...";
    logoOpacity = 1;
    logoPosition = br;
    m3u8Url = "https://...";
    m3u8Url1 = "";
    m3u8Url2 = "";
    m3u8Url3 = "";
    marquee = "";
    marqueeAutoZoomEnabled = N;
    marqueeFontColor = "";
    marqueeFontSize = "-1";
    marqueeOpacity = "";
    marqueeSetting = 1;
    marqueeSign = "";
    marqueeType = "";
    multirateEnabled = N;
    multirateModel = "<null>";
    multirates = "";
    name = "IE\U4e03\U5927\U624b\U6cd5(ACKN)";
    ngbUrl = "https://...";
    passwdEncrypted = "";
    passwdRestrict = 0;
    playerColor = "#666666";
    playerPreloadEnabled = Y;
    pptDelayTime =     (
                {
            mobile = 9;
            pc = 4;
        },
                {
            mobile = 8;
            pc = 3;
        }
    );
    pullProxyEnabled = N;
    recordFileDuration = "-1";
    recordFileM3u8Url = "";
    recordFileSessionId = "";
    recordFileUrl = "";
    recordingProtectEnabled = N;
    reportFreq = 10;
    rightMenu = "";
    rightMenuHref = "";
    rtmpUrl = "rtmp://...";
    sceneEnabled = N;
    screenCastEnabled = N;
    stopAdvertClickUrl = "";
    stopAdvertHref = "";
    stopAdvertImage = "";
    stopAdvertShowUrl = "";
    stream = 96d8d4007c159678134282705a9;
    streamSign = "&auth_key=1598340295-0-0-54559fa80c426ec7c36d870d18cad7ca";
    streamType = client;
    subChannels =     (
    );
    txPullUrl = "<null>";
    url = "https://...";
    useHls = N;
    userId = 96d8d4007c;
    waitHref = "";
    waitImage = "";
    warmUpFlv = "";
    webappCustomDomain = "live.polyv.net";
}

ChannelJson 数据示例见上 */
@end
