# TRTC 音频混音技术方案 - 基于音频数据回调的同步混音

## ⚠️ 重要说明

本技术方案专门解决 TRTC 音频混音中的**音视频同步问题**。传统的 `TXAudioEffectManager` 方案在推流场景中会导致音画不同步，本方案通过音频数据回调实现真正的实时同步混音。

## 技术方案对比

### ❌ 问题方案：TXAudioEffectManager
```objective-c
// 会导致音视频不同步的方案
TRTCAudioMusicParam *musicParam = [[TRTCAudioMusicParam alloc] init];
[audioEffectManager startPlayMusic:musicParam onProgress:nil onComplete:nil];
```

**问题**：
- 背景音乐播放和麦克风采集存在不同延迟
- SDK内部混音与视频流同步性无法保证
- 推流时音画不同步现象明显

### ✅ 正确方案：音频数据回调同步混音
```objective-c
// 在音频数据回调中进行实时混音
- (void)onCapturedRawAudioFrame:(TRTCAudioFrame *)frame {
    if (self.audioMixingEnabled && [self isBackgroundMusicPlaying]) {
        [self mixBackgroundMusicWithAudioFrame:frame];
    }
}
```

**优势**：
- 音频混音与视频流完全同步
- 延迟一致性，无音画不同步问题
- 精确的音频数据控制

## 核心技术实现

### 1. 架构设计

```objective-c
#pragma mark 音频混音 - 基于音频数据回调的同步混音方案
@property (nonatomic, assign) BOOL audioMixingEnabled;
@property (nonatomic, strong) AVAudioFile *backgroundMusicFile;
@property (nonatomic, strong) AVAudioPCMBuffer *backgroundMusicBuffer;
@property (nonatomic, assign) NSUInteger backgroundMusicSampleIndex;
@property (nonatomic, assign) NSUInteger backgroundMusicTotalSamples;
@property (nonatomic, assign) float microphoneVolume; // 0.0-1.0
@property (nonatomic, assign) float backgroundMusicVolume; // 0.0-1.0
@property (nonatomic, assign) BOOL backgroundMusicLoop;
@property (nonatomic, assign) BOOL backgroundMusicPlaying;
@property (nonatomic, assign) BOOL backgroundMusicPaused;
```

### 2. 音频文件加载

```objective-c
/// 加载背景音乐文件
- (int)loadBackgroundMusic:(NSString *)musicPath {
    NSError *error = nil;
    NSURL *musicURL = [NSURL fileURLWithPath:musicPath];
    
    // 加载音频文件
    self.backgroundMusicFile = [[AVAudioFile alloc] initForReading:musicURL error:&error];
    if (error || !self.backgroundMusicFile) {
        return -1;
    }
    
    // 创建音频缓冲区
    AVAudioFrameCount frameCount = (AVAudioFrameCount)self.backgroundMusicFile.length;
    self.backgroundMusicBuffer = [[AVAudioPCMBuffer alloc] 
        initWithPCMFormat:self.backgroundMusicFile.processingFormat 
        frameCapacity:frameCount];
    
    // 读取音频数据到缓冲区
    BOOL success = [self.backgroundMusicFile readIntoBuffer:self.backgroundMusicBuffer error:&error];
    if (!success || error) {
        return -1;
    }
    
    self.backgroundMusicTotalSamples = self.backgroundMusicBuffer.frameLength;
    self.backgroundMusicSampleIndex = 0;
    
    return 0;
}
```

### 3. 实时音频混合

```objective-c
/// 在音频数据回调中进行实时混音
- (void)mixBackgroundMusicWithAudioFrame:(TRTCAudioFrame *)frame {
    if (!self.backgroundMusicPlaying || self.backgroundMusicPaused || !self.backgroundMusicBuffer) {
        return;
    }
    
    // 确保采样率匹配（TRTC通常使用48kHz）
    if (self.backgroundMusicFile.fileFormat.sampleRate != frame.sampleRate) {
        return; // 采样率不匹配，需要重采样
    }
    
    // 获取音频数据指针
    int16_t *micData = (int16_t *)frame.data;
    float *musicData = self.backgroundMusicBuffer.floatChannelData[0];
    
    NSUInteger frameSamples = frame.length / sizeof(int16_t);
    NSUInteger remainingMusicSamples = self.backgroundMusicTotalSamples - self.backgroundMusicSampleIndex;
    NSUInteger samplesToMix = MIN(frameSamples, remainingMusicSamples);
    
    // 进行音频混合
    for (NSUInteger i = 0; i < samplesToMix; i++) {
        // 将背景音乐从float格式转换为int16格式
        float musicSample = musicData[self.backgroundMusicSampleIndex + i] * self.backgroundMusicVolume;
        int16_t musicSampleInt16 = (int16_t)(musicSample * 32767.0f);
        
        // 混合麦克风音频（应用麦克风音量）
        int32_t micSample = (int32_t)(micData[i] * self.microphoneVolume);
        
        // 混合两路音频
        int32_t mixedSample = micSample + musicSampleInt16;
        
        // 防止溢出
        if (mixedSample > INT16_MAX) {
            mixedSample = INT16_MAX;
        } else if (mixedSample < INT16_MIN) {
            mixedSample = INT16_MIN;
        }
        
        micData[i] = (int16_t)mixedSample;
    }
    
    // 更新背景音乐播放位置
    self.backgroundMusicSampleIndex += samplesToMix;
    
    // 处理循环播放
    if (self.backgroundMusicSampleIndex >= self.backgroundMusicTotalSamples) {
        if (self.backgroundMusicLoop) {
            self.backgroundMusicSampleIndex = 0; // 重新开始
        } else {
            self.backgroundMusicPlaying = NO; // 停止播放
        }
    }
}
```

### 4. 音频数据回调集成

```objective-c
/// 启用音频混音功能
- (void)enableAudioMixing:(BOOL)enabled {
    self.audioMixingEnabled = enabled;
    if (enabled) {
        // 启用音频数据回调
        [self.engine setAudioFrameDelegate:self];
    } else {
        // 停止背景音乐并禁用音频数据回调
        [self stopBackgroundMusic];
        [self.engine setAudioFrameDelegate:nil];
    }
}

#pragma mark - TRTCAudioFrameDelegate

/// 本地麦克风采集到的音频数据回调
- (void)onCapturedRawAudioFrame:(TRTCAudioFrame *)frame {
    if (self.audioMixingEnabled && [self isBackgroundMusicPlaying]) {
        [self mixBackgroundMusicWithAudioFrame:frame];
    }
}
```

## 使用方法

### 1. 基本使用流程

```objective-c
// 1. 启用音频混音
PLVBLinkMicTRTCManager *manager = [[PLVBLinkMicTRTCManager alloc] init];
[manager enableAudioMixing:YES];

// 2. 播放背景音乐
NSString *musicPath = [[NSBundle mainBundle] pathForResource:@"background" ofType:@"mp3"];
[manager startBackgroundMusic:musicPath loopEnabled:YES];

// 3. 控制音量
[manager setMicrophoneVolume:0.8f];        // 麦克风音量 80%
[manager setBackgroundMusicVolume:0.5f];   // 背景音乐音量 50%

// 4. 播放控制
[manager pauseBackgroundMusic];    // 暂停
[manager resumeBackgroundMusic];   // 恢复
[manager stopBackgroundMusic];     // 停止
```

### 2. 进度控制

```objective-c
// 获取播放信息
double duration = [manager getBackgroundMusicDuration];        // 总时长（秒）
double progress = [manager getBackgroundMusicProgress];        // 当前进度（秒）
BOOL isPlaying = [manager isBackgroundMusicPlaying];          // 是否正在播放

// 跳转到指定位置
[manager setBackgroundMusicProgress:30.0];  // 跳转到30秒位置

// 音量动态调整
[manager setMicrophoneVolume:0.9f];         // 调整麦克风音量
[manager setBackgroundMusicVolume:0.3f];    // 调整背景音乐音量
```

## 技术优势

### 1. **完美的音视频同步**
- 音频混音在 TRTC 音频数据回调中进行
- 与视频流使用相同的时间基准
- 无音画不同步问题

### 2. **精确的时序控制**
- 采样级别的精确混音
- 支持实时音量调节
- 支持循环播放和进度控制

### 3. **高性能处理**
- 直接在音频数据流中混音，无额外延迟
- 内存高效，预加载音频到缓冲区
- CPU 占用低，优化的混音算法

### 4. **格式兼容性**
- 支持 MP3、AAC、WAV 等常见格式
- 自动格式转换和采样率匹配
- 支持单声道和立体声

## 应用场景

### 1. **直播推流**
- 主播背景音乐 + 实时语音
- 确保观众看到的音视频完全同步
- 支持音量平衡调节

### 2. **在线教育**
- 老师讲课 + 课件音频
- 学生端音视频同步体验
- 支持音频素材播放

### 3. **视频通话**
- 通话中播放音乐或音效
- 多人通话音频混音
- 个性化音频体验

## 注意事项

### 1. **音频格式要求**
- 建议使用与 TRTC 相同的采样率（48kHz）
- 支持单声道音频文件
- 文件大小控制，避免内存过大

### 2. **性能优化**
- 音频文件预加载到内存
- 避免在混音回调中进行耗时操作
- 及时释放不用的音频资源

### 3. **错误处理**
- 检查音频文件加载是否成功
- 处理采样率不匹配的情况
- 监控音频播放状态

## 技术对比总结

| 特性 | 音频数据回调方案 | TXAudioEffectManager |
|------|----------------|---------------------|
| **音视频同步** | ✅ 完美同步 | ❌ 可能不同步 |
| **推流同步** | ✅ 保证同步 | ❌ 存在延迟差异 |
| **精确控制** | ✅ 采样级精确 | ⚠️ 有限控制 |
| **性能开销** | 🟢 低 | 🟢 低 |
| **开发复杂度** | 🟡 中等 | 🟢 简单 |
| **稳定性** | ✅ 高 | ✅ 高 |

## 总结

基于音频数据回调的 TRTC 混音方案是目前**唯一能够保证音视频完美同步**的技术方案。虽然实现相比 `TXAudioEffectManager` 稍微复杂，但在推流场景中的音视频同步效果是无可替代的。

这套方案已在 `PLVBLinkMicTRTCManager` 中完整实现，可以直接使用，**强烈推荐用于所有需要音视频同步的直播和推流场景**。 