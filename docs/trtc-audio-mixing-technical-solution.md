# TRTC 音频混音技术方案调研

## 方案概述

本文档调研了TRTC（腾讯云实时音视频）SDK在音频混音方面的技术能力，分析了现有项目实现，并提出推荐的技术方案。

## TRTC 官方音频混音能力分析

### 1. 原生混音功能

#### 1.1 云端混流转码
- **功能描述**: TRTC提供云端混流服务，支持将多路音视频流在云端进行混合
- **适用场景**: 需要录制混合后的流、CDN推流等
- **技术特点**:
  - 支持最多16路流同时混流
  - 支持音视频、纯音频、纯视频等多种输入源
  - 提供多种预设布局模板
  - 支持自定义混流布局

#### 1.2 本地音频处理
TRTC SDK在本地音频处理方面的能力：
- **音频采集**: 支持麦克风音频采集
- **音频预处理**: 支持噪声抑制、回声消除、音量调节
- **音频编码**: 支持多种音频编码格式

### 2. Web端混音插件 (AudioMixerPlugin)

TRTC Web SDK 提供了专门的 `AudioMixerPlugin` 插件用于音频混音：

#### 2.1 核心功能
```javascript
// 创建音频源
let audioSource = AudioMixerPlugin.createAudioSource({
    url: 'background-music.mp3',
    loop: true,
    volume: 0.5
});

// 混合音频轨道
let mixedTrack = await AudioMixerPlugin.mix({
    targetTrack: microphoneTrack,
    sourceList: [audioSource]
});

// 替换音频轨道
await localStream.replaceTrack(mixedTrack);
```

#### 2.2 支持的混音场景
1. **背景音乐混音**: 将背景音乐与麦克风音频混合
2. **系统声音采集**: 同时采集麦克风和系统音频
3. **音效叠加**: 支持多个音效同时播放和混合
4. **实时音量控制**: 支持动态调整各音源音量

### 3. iOS/Android 原生混音API

#### 3.1 iOS SDK音频混音能力
```objective-c
// 音频采集相关
- (int)enableLocalAudio:(BOOL)enabled;
- (int)muteLocalAudioStream:(BOOL)mute;
- (int)setAudioCaptureVolume:(NSInteger)volume;

// 音频播放相关  
- (int)setAudioPlayoutVolume:(NSInteger)volume;
- (int)enableAudioVolumeEvaluation:(NSUInteger)interval;

// 背景音乐相关
- (int)startPlayMusic:(TRTCAudioMusicParam *)musicParam;
- (int)stopPlayMusic:(int32_t)musicID;
- (int)pausePlayMusic:(int32_t)musicID;
- (int)resumePlayMusic:(int32_t)musicID;
- (int)setMusicVolume:(int32_t)musicID volume:(float)volume;
```

#### 3.2 音频数据处理回调
```objective-c
// 音频数据预处理回调
- (void)onCapturedRawAudioFrame:(TRTCAudioFrame *)frame;

// 音频数据后处理回调  
- (void)onLocalProcessedAudioFrame:(TRTCAudioFrame *)frame;

// 混音前的音频数据
- (void)onPlayAudioFrame:(TRTCAudioFrame *)frame userId:(NSString *)userId;

// 混音后的音频数据
- (void)onMixedPlayAudioFrame:(TRTCAudioFrame *)frame;
```

## 项目现有实现分析

### 1. 已实现的音频混音组件

#### 1.1 PLVSAVideoMaterialMixer
```objective-c
@interface PLVSAVideoMaterialMixer : NSObject

/// 素材音频音量（0.0 - 1.0）
@property (nonatomic, assign) CGFloat materialAudioVolume;

/// 麦克风音频音量（0.0 - 1.0）  
@property (nonatomic, assign) CGFloat microphoneAudioVolume;

/// 混合音频数据
- (CMSampleBufferRef)mixMaterialAudio:(CMSampleBufferRef)materialAudio 
                  withMicrophoneAudio:(CMSampleBufferRef)microphoneAudio;

@end
```

#### 1.2 实现特点
- **数据级混音**: 直接在CMSampleBuffer层面进行音频数据混合
- **音量控制**: 支持独立的音量调节
- **实时处理**: 与视频播放同步的音频混音
- **防溢出**: 实现了音频混合的防溢出算法

### 2. TRTC集成现状

#### 2.1 PLVBLinkMicTRTCManager 音频能力
```objective-c
// 现有音频相关方法
- (int)openLocalUserMic:(BOOL)openMic;
- (int)enableLocalAudio:(BOOL)enabled;
- (int)muteLocalAudioStream:(BOOL)mute;
- (int)changeLocalMicVolume:(CGFloat)micVolume;
- (int)switchNoiseCancellationLevelTo:(PLVBLinkMicNoiseCancellationLevel)level;
```

#### 2.2 音频数据处理
- **音量检测**: 实现了实时音量检测和回调
- **噪声控制**: 支持多级噪声抑制
- **音频质量**: 支持不同音频质量配置

## 推荐技术方案

### 方案一：基于TRTC音频数据回调的本地混音

#### 1.1 架构设计
```objective-c
@interface PLVBTRTCAudioMixer : NSObject

// 音频源管理
@property (nonatomic, strong) NSMutableArray<PLVBAudioSource *> *audioSources;

// 混音配置
@property (nonatomic, assign) CGFloat masterVolume;
@property (nonatomic, assign) CGFloat microphoneVolume;

// 核心混音方法
- (CMSampleBufferRef)processAudioFrame:(CMSampleBufferRef)microphoneFrame;
- (void)addAudioSource:(PLVBAudioSource *)audioSource;
- (void)removeAudioSource:(NSString *)sourceId;
- (void)setVolumeForSource:(NSString *)sourceId volume:(CGFloat)volume;

@end
```

#### 1.2 音频源抽象
```objective-c
@interface PLVBAudioSource : NSObject

@property (nonatomic, copy) NSString *sourceId;
@property (nonatomic, assign) CGFloat volume;
@property (nonatomic, assign) BOOL isPlaying;
@property (nonatomic, strong) AVAudioPlayer *player;

// 获取当前时刻的音频数据
- (CMSampleBufferRef)getCurrentAudioFrame:(CMTime)timestamp;

@end
```

#### 1.3 集成到TRTC
```objective-c
// 在PLVBLinkMicTRTCManager中扩展
@interface PLVBLinkMicTRTCManager (AudioMixing)

@property (nonatomic, strong) PLVBTRTCAudioMixer *audioMixer;

// 混音控制接口
- (void)enableAudioMixing:(BOOL)enabled;
- (void)addBackgroundMusic:(NSString *)filePath volume:(CGFloat)volume;
- (void)removeBackgroundMusic:(NSString *)sourceId;
- (void)setMicrophoneVolume:(CGFloat)volume;
- (void)setBackgroundMusicVolume:(NSString *)sourceId volume:(CGFloat)volume;

@end
```

#### 1.4 实现流程
1. **音频采集**: 通过TRTC的音频采集获取麦克风数据
2. **数据混音**: 在音频预处理回调中进行混音
3. **数据输出**: 将混音后的数据发送给TRTC进行编码传输

### 方案二：基于TRTC Web AudioMixerPlugin的混音

#### 2.1 技术特点
- **官方支持**: 使用TRTC官方提供的混音插件
- **浏览器兼容**: 支持主流浏览器的音频混音
- **功能完整**: 支持背景音乐、音效、系统声音等多种混音场景

#### 2.2 实现示例
```javascript
// 创建背景音乐源
const backgroundMusic = AudioMixerPlugin.createAudioSource({
    url: 'background.mp3',
    loop: true,
    volume: 0.3
});

// 创建音效源
const soundEffect = AudioMixerPlugin.createAudioSource({
    url: 'sound-effect.mp3',
    volume: 0.8
});

// 获取麦克风轨道
const micTrack = localStream.getAudioTrack();

// 混合音频
const mixedTrack = await AudioMixerPlugin.mix({
    targetTrack: micTrack,
    sourceList: [backgroundMusic, soundEffect]
});

// 替换音频轨道
await localStream.replaceTrack(mixedTrack);

// 播放控制
backgroundMusic.play();
soundEffect.play();
```

### 方案三：云端混流方案

#### 3.1 适用场景
- 需要录制混合后的音频
- 需要推流到CDN
- 多人音频混合
- 减轻客户端处理负担

#### 3.2 实现方式
```objective-c
// 通过TRTC云端混流API
- (void)startCloudMixTranscoding {
    TRTCTranscodingConfig *config = [[TRTCTranscodingConfig alloc] init];
    config.appId = self.appId;
    config.bizId = self.bizId;
    config.mode = TRTCTranscodingConfigMode_Manual;
    
    // 配置音频混流参数
    config.audioSampleRate = 48000;
    config.audioBitrate = 64;
    config.audioChannels = 1;
    
    // 配置混流用户
    TRTCMixUser *mainUser = [[TRTCMixUser alloc] init];
    mainUser.userId = self.localUserId;
    mainUser.roomId = self.roomId;
    
    config.mixUsers = @[mainUser];
    
    [self.trtcCloud setMixTranscodingConfig:config];
}
```

## 性能与兼容性分析

### 1. 性能对比

| 方案 | CPU占用 | 内存占用 | 延迟 | 网络开销 |
|-----|--------|----------|------|----------|
| 本地混音 | 中等 | 低 | 极低 | 无额外开销 |
| Web混音插件 | 低 | 低 | 低 | 无额外开销 |
| 云端混流 | 极低 | 极低 | 中等 | 有额外开销 |

### 2. 兼容性分析

#### 2.1 平台兼容性
- **iOS**: 支持iOS 9.0+，所有方案均可使用
- **Android**: 支持Android 4.4+，所有方案均可使用  
- **Web**: 仅支持Web混音插件方案
- **小程序**: 限制较多，建议使用云端混流

#### 2.2 设备兼容性
- **低端设备**: 推荐云端混流方案
- **中高端设备**: 推荐本地混音方案
- **Web环境**: 必须使用Web混音插件

## 实施建议

### 1. 分阶段实施

#### 第一阶段：基础混音能力
- 实现PLVBTRTCAudioMixer基础混音器
- 支持背景音乐与麦克风混音
- 基本的音量控制和播放控制

#### 第二阶段：高级功能
- 支持多音频源同时混音
- 实现音频淡入淡出效果
- 添加音频格式转换支持

#### 第三阶段：优化与扩展  
- 性能优化和内存管理
- 错误处理和异常恢复
- 与其他功能模块的深度集成

### 2. 技术选型建议

#### 2.1 移动端推荐
- **主要方案**: 本地混音 (方案一)
- **备选方案**: 云端混流 (方案三)
- **选择依据**: 
  - 实时性要求高：选择本地混音
  - 设备性能限制：选择云端混流
  - 需要录制：选择云端混流

#### 2.2 Web端推荐
- **唯一方案**: AudioMixerPlugin (方案二)
- **实施重点**: 浏览器兼容性测试和优化

### 3. 关键技术难点

#### 3.1 音频同步
- **问题**: 多音频源之间的时间同步
- **解决**: 使用统一的时间戳基准，实现精确的音频对齐

#### 3.2 音质保持
- **问题**: 混音过程中的音质损失
- **解决**: 使用高精度浮点运算，合理的音频格式转换

#### 3.3 实时性保证
- **问题**: 混音处理延迟
- **解决**: 优化混音算法，使用多线程处理

## 总结

TRTC支持多种音频混音技术方案，各有优劣：

1. **本地混音方案**：适合对实时性要求高的场景，技术复杂度中等
2. **Web混音插件**：Web端的最佳选择，功能完整，使用简单
3. **云端混流方案**：适合录制和CDN推流场景，减轻客户端负担

建议根据具体业务需求和技术约束选择合适的方案，并可以组合使用以达到最佳效果。项目中已有的音频混音基础实现为进一步开发提供了良好的基础。 