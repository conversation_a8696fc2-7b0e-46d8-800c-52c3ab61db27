//
//  PLVBLinkMicTRTCManager-AudioMixingUsageExample.m
//  PolyvBusinessSDK
//
//  TRTC自定义音频混音功能使用示例
//  Created by <PERSON> on 2025-01-10.
//
//  基于enableMixExternalAudioFrame的PCM音频混音方案
//  通过"蓄水池"缓冲机制实现稳定的音频混音
//

#import "PLVBLinkMicTRTCManager.h"
#import <AudioToolbox/AudioToolbox.h>
#import <AVFoundation/AVFoundation.h>

/*
 TRTC自定义音频混音功能使用示例
 
 本示例展示了如何使用PLVBLinkMicTRTCManager的自定义音频混音功能，
 包括如何开启混音、准备音频数据、执行混音操作等。
 
 主要功能：
 1. 开启/关闭外部音频混音
 2. 准备PCM格式音频数据
 3. 执行音频混音操作
 4. 监控音频缓冲区状态
 */

@interface AudioMixingExample : NSObject {
    PLVBLinkMicTRTCManager *_trtcManager;
    NSTimer *_audioMixingTimer;
    AudioFileID _audioFileID;
    UInt64 _audioDataOffset;
}

@end

@implementation AudioMixingExample

#pragma mark - 初始化和配置

- (void)setupAudioMixing {
    // 1. 创建TRTC管理器实例
    _trtcManager = [[PLVBLinkMicTRTCManager alloc] init];
    
    // 2. 开启外部音频混音功能
    BOOL success = [_trtcManager enableMixExternalAudioFrame:YES];
    if (success) {
        NSLog(@"✅ 外部音频混音已开启");
    } else {
        NSLog(@"❌ 外部音频混音开启失败");
        return;
    }
    
    // 3. 准备音频文件（可选）
    [self prepareAudioFile];
    
    // 4. 启动音频混音定时器
    [self startAudioMixingTimer];
}

- (void)prepareAudioFile {
    // 示例：准备一个音频文件用于混音
    NSString *audioFilePath = [[NSBundle mainBundle] pathForResource:@"background_music" ofType:@"wav"];
    if (!audioFilePath) {
        NSLog(@"⚠️  背景音乐文件未找到，将使用生成的音频数据");
        return;
    }
    
    NSURL *audioFileURL = [NSURL fileURLWithPath:audioFilePath];
    OSStatus status = AudioFileOpenURL((__bridge CFURLRef)audioFileURL, kAudioFileReadPermission, 0, &_audioFileID);
    if (status != noErr) {
        NSLog(@"❌ 音频文件打开失败: %d", (int)status);
    } else {
        NSLog(@"✅ 音频文件准备完成");
    }
}

#pragma mark - 音频混音操作

- (void)startAudioMixingTimer {
    // 每20ms触发一次音频混音（对应48kHz采样率的20ms帧长度）
    _audioMixingTimer = [NSTimer scheduledTimerWithTimeInterval:0.02
                                                         target:self
                                                       selector:@selector(performAudioMixing)
                                                       userInfo:nil
                                                        repeats:YES];
}

- (void)performAudioMixing {
    // 创建音频帧数据
    TRTCAudioFrame *audioFrame = [self createAudioFrame];
    if (!audioFrame) {
        return;
    }
    
    // 执行音频混音
    NSInteger bufferDuration = [_trtcManager mixExternalAudioFrame:audioFrame];
    
    // 监控缓冲区状态
    [self monitorAudioBuffer:bufferDuration];
    
    // 释放音频帧数据
    if (audioFrame.data) {
        free(audioFrame.data);
    }
}

- (TRTCAudioFrame *)createAudioFrame {
    // 音频参数配置
    const int sampleRate = 48000;    // 采样率
    const int channels = 1;          // 单声道
    const int frameLength = 20;      // 20ms帧长度
    const int samplesPerFrame = sampleRate * frameLength / 1000;
    const int bytesPerFrame = samplesPerFrame * channels * sizeof(int16_t);
    
    // 分配音频数据内存
    uint8_t *audioData = (uint8_t *)malloc(bytesPerFrame);
    if (!audioData) {
        return nil;
    }
    
    // 填充音频数据
    if (_audioFileID) {
        // 从文件读取音频数据
        [self readAudioDataFromFile:audioData length:bytesPerFrame];
    } else {
        // 生成测试音频数据（440Hz正弦波）
        [self generateTestAudioData:audioData length:bytesPerFrame sampleRate:sampleRate channels:channels];
    }
    
    // 创建TRTCAudioFrame
    TRTCAudioFrame *audioFrame = [[TRTCAudioFrame alloc] init];
    audioFrame.data = audioData;
    audioFrame.length = bytesPerFrame;
    audioFrame.sampleRate = sampleRate;
    audioFrame.channels = channels;
    audioFrame.timestamp = [_trtcManager generateCustomPTS];
    
    return audioFrame;
}

- (void)readAudioDataFromFile:(uint8_t *)buffer length:(int)length {
    UInt32 bytesToRead = length;
    OSStatus status = AudioFileReadBytes(_audioFileID, false, _audioDataOffset, &bytesToRead, buffer);
    if (status == noErr) {
        _audioDataOffset += bytesToRead;
        // 如果读取的数据不足，用零填充
        if (bytesToRead < length) {
            memset(buffer + bytesToRead, 0, length - bytesToRead);
        }
    } else {
        // 读取失败，用零填充
        memset(buffer, 0, length);
    }
}

- (void)generateTestAudioData:(uint8_t *)buffer length:(int)length sampleRate:(int)sampleRate channels:(int)channels {
    int16_t *samples = (int16_t *)buffer;
    int sampleCount = length / sizeof(int16_t);
    
    static double phase = 0.0;
    const double frequency = 440.0; // 440Hz 音调
    const double amplitude = 0.3;   // 音量
    
    for (int i = 0; i < sampleCount; i++) {
        double sample = sin(phase) * amplitude;
        samples[i] = (int16_t)(sample * INT16_MAX);
        phase += 2.0 * M_PI * frequency / sampleRate;
        if (phase >= 2.0 * M_PI) {
            phase -= 2.0 * M_PI;
        }
    }
}

#pragma mark - 监控和管理

- (void)monitorAudioBuffer:(NSInteger)bufferDuration {
    if (bufferDuration > 150) {
        // 缓冲区过大，可以适当减少混音频率
        NSLog(@"⚠️  音频缓冲区较大: %ldms", (long)bufferDuration);
    } else if (bufferDuration < 30) {
        // 缓冲区过小，需要增加混音频率
        NSLog(@"⚠️  音频缓冲区较小: %ldms", (long)bufferDuration);
    }
    
    // 根据缓冲区状态调整混音策略
    if (bufferDuration > 100) {
        // 缓冲区充足，可以暂停一帧
        [NSThread sleepForTimeInterval:0.02];
    }
}

#pragma mark - 清理资源

- (void)stopAudioMixing {
    // 停止定时器
    if (_audioMixingTimer) {
        [_audioMixingTimer invalidate];
        _audioMixingTimer = nil;
    }
    
    // 关闭外部音频混音
    [_trtcManager enableMixExternalAudioFrame:NO];
    
    // 关闭音频文件
    if (_audioFileID) {
        AudioFileClose(_audioFileID);
        _audioFileID = NULL;
    }
    
    NSLog(@"✅ 音频混音已停止");
}

@end

#pragma mark - 使用示例

/*
 使用示例：
 
 1. 基本使用：
 
 ```objc
 // 创建示例实例
 AudioMixingExample *example = [[AudioMixingExample alloc] init];
 
 // 开始音频混音
 [example setupAudioMixing];
 
 // 停止音频混音
 [example stopAudioMixing];
 ```
 
 2. 高级使用：
 
 ```objc
 // 直接使用TRTC管理器
 PLVBLinkMicTRTCManager *trtcManager = [[PLVBLinkMicTRTCManager alloc] init];
 
 // 开启混音
 [trtcManager enableMixExternalAudioFrame:YES];
 
 // 准备音频数据
 TRTCAudioFrame *audioFrame = [[TRTCAudioFrame alloc] init];
 audioFrame.data = audioData;          // PCM数据
 audioFrame.length = dataLength;       // 数据长度
 audioFrame.sampleRate = 48000;        // 采样率
 audioFrame.channels = 1;              // 声道数
 audioFrame.timestamp = [trtcManager generateCustomPTS]; // 时间戳
 
 // 执行混音
 NSInteger bufferDuration = [trtcManager mixExternalAudioFrame:audioFrame];
 
 // 根据返回的缓冲区时长调整混音策略
 if (bufferDuration > 100) {
     // 缓冲区充足，可以等待
 } else if (bufferDuration < 50) {
     // 缓冲区不足，需要加快混音
 }
 ```
 
 3. 音频参数要求：
 
 - 采样率：16000、24000、32000、44100、48000 Hz
 - 声道数：1（单声道）或 2（双声道）
 - 采样位深：16位
 - 帧长度：5ms - 100ms（推荐20ms）
 - 数据格式：PCM
 
 4. 最佳实践：
 
 - 保持稳定的调用间隔（推荐20ms）
 - 监控缓冲区状态，动态调整混音策略
 - 及时释放音频数据内存
 - 在应用进入后台时暂停混音
 - 处理音频中断和路由变化
 
 5. 性能优化：
 
 - 使用异步队列处理音频数据
 - 预分配音频缓冲区，避免频繁内存分配
 - 使用高效的音频混音算法
 - 避免在主线程进行音频处理
 */